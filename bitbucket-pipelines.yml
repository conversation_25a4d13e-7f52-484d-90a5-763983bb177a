image: node:20

definitions:
  steps:
    - step: &build-and-deploy
        name: Build & Deploy to Lambda
        caches:
          - node
        script:
          - apt-get update && apt-get install -y zip awscli
          - aws configure list
          - npm ci
          - npm run build # assumes you have `dist/main.js`
          - npm prune --omit=dev
          - cp -r node_modules dist/node_modules
          - cd dist
          - zip -r ../lambda.zip .
          - cd ..
          - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
          - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
          - aws configure set default.region $AWS_DEFAULT_REGION
          - aws lambda update-function-code --function-name $LAMBDA_FUNCTION_NAME --zip-file fileb://lambda.zip
          - echo "Upload the zip to S3."
          - aws s3 cp lambda.zip s3://airpay-capital-api-cicd/los/app-dist.zip
          - aws configure list
          - echo "Download the zip on EC2 instance."
          - >
            aws ssm send-command \
              --document-name "AWS-RunShellScript" \
              --instance-ids "i-00aa7353cca47feb4" \
              --parameters 'commands=[
                "#!/bin/bash",
                "export HOME=/root",
                "aws s3 cp s3://airpay-capital-api-cicd/los/app-dist.zip /srv/www/app/los/app-dist.zip",
                "unzip -o /srv/www/app/los/app-dist.zip -d /srv/www/app/los/dist",
                "aws s3 cp s3://airpay-capital-api-cicd/los/.env /srv/www/app/los/dist/.env",
                "aws s3 cp s3://airpay-capital-api-cicd/los/ecosystem.config.js /srv/www/app/los/dist/ecosystem.config.js",
                "cd /srv/www/app/los/dist",
              ]' \
              --output text

pipelines:
  branches:
    # master:
    #   - step: *build-and-deploy
    loan-application:
      - step: *build-and-deploy
