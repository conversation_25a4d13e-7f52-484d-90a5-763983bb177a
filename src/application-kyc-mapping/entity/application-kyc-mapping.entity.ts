import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';

@Entity('application_kyc_mapping')
export class ApplicationKycMapping extends CustomBaseEntity {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @Column({ name: 'kyc_id', type: 'int' })
  kycId: number;

  @Column({ name: 'type', type: 'varchar', length: 100 })
  type: string;

  @ManyToOne(() => LoanApplication)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;
}
