// loan_application.entity.ts
import {
  Enti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  OneToOne,
  OneToMany,
} from 'typeorm';
import { Product } from 'src/product/entity/product.entity';
import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';
import { LoanStatusMaster } from 'src/loan-status-master/entity/loan-status-master.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { ApplicantDetail } from '../../applicant-details/entity/applicant-details.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { ApplicationDocument } from 'src/application-documents/entity/application-document.entity';
import { UserConsent } from 'src/user-consent/entity/user-consent.entity';
import { LoanOffer } from 'src/loan-offer/entity/loan-offer.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import { IncomeVerification } from 'src/income-verification/entities/income-verification.entity';
import { LendingPartner } from 'src/lending-partners/entity/lending-partner.entity';
import { ApplicationRemarks } from 'src/application-event-log/entity/application-remarks.entity';
import { DsaAgent } from 'src/dsa-management/entity/dsa-agents.entity';
import { BusinessDetail } from 'src/business-details/entity/business-details.entity';
import { BusinessDirector } from 'src/business-details/entity/business-director.entity';

export enum ApplicationSubStatus {
  DIGILOCKER_KYC = 'DIGILOCKER_KYC',
  PAN = 'PAN',
  PERSONAL_INFO = 'PERSONAL_INFO',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  WORK_DETAILS = 'WORK_DETAILS',
  ADDRESS_VERIFICATION = 'ADDRESS_VERIFICATION',
  INCOME_VERIFICATION = 'INCOME_VERIFICATION',
  LOAN_OFFER_ACCEPTED = 'LOAN_OFFER_ACCEPTED',
  VIDEO_KYC_SUCCESS = 'VIDEO_KYC_SUCCESS',
  CREDIT_OFFICER_APPROVED = 'CREDIT_OFFICER_APPROVED',
  CREDIT_OFFICER_REJECTED = 'CREDIT_OFFICER_REJECTED',
  NACH_MANDATE = 'NACH_MANDATE',
  INSURANCE_NOMINEE = 'INSURANCE_NOMINEE',
  LOAN_DOCUMENT_ACCEPTED = 'LOAN_DOCUMENT_ACCEPTED',
  DOCUMENTS_SIGNED = 'DOCUMENTS_SIGNED',
  ELIGIBILITY_SHOWN = 'ELIGIBILITY_SHOWN',
  SENIOR_OFFICER_REJECTED = 'SENIOR_OFFICER_REJECTED',
  BAD_CIBIL = 'BAD_CIBIL',
  CIBIL_ACCEPTED = 'CIBIL_ACCEPTED',
  BANK_ACCOUNT_VERIFIED = 'BANK_ACCOUNT_VERIFIED',
  UNDERWRITING_REJECTED = 'UNDERWRITING_REJECTED',
  OFFER_EXPIRED = 'OFFER_EXPIRED',
  ADDRESS_VERIFICATION_FAILED = 'ADDRESS_VERIFICATION_FAILED',
  BANK_VERIFICATION_FAILED = 'BANK_VERIFICATION_FAILED',
}

@Entity({ name: 'loan_applications' })
@Index(['userId', 'createdAt']) // index for frequently filtered by createdAt
export class LoanApplication extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_id', type: 'int' })
  productId: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Index() // frequent filter by userId
  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Index({ unique: true })
  @Column({ name: 'application_ref_num', type: 'varchar', unique: true })
  applicationRefNum: string;

  @Column({ name: 'loan_purpose_id', type: 'int' })
  loanPurposeId: number;

  @ManyToOne(() => LoanPurposeMaster)
  @JoinColumn({ name: 'loan_purpose_id' })
  loanPurpose: LoanPurposeMaster;

  @Column({ name: 'sentry_risk', type: 'varchar', nullable: true })
  sentryRisk?: string;

  @Index('IDX_USER_STATUS_ID')
  @Column({ name: 'status_id', type: 'int' })
  statusId: number;

  @ManyToOne(() => LoanStatusMaster)
  @JoinColumn({ name: 'status_id' })
  status: LoanStatusMaster;

  @Column({ name: 'loan_account_number', type: 'varchar', nullable: true })
  loanAccountNumber?: string;

  @Column({ name: 'applicant_type', type: 'varchar' })
  applicantType: string;

  @OneToOne(() => ApplicantDetail, (applicant) => applicant.application)
  applicantDetail: ApplicantDetail;

  @Index()
  @Column({ name: 'sub_status', type: 'varchar', nullable: true })
  subStatus: ApplicationSubStatus;

  @OneToMany(() => ApplicationEventLog, (log) => log.application)
  eventLogs: ApplicationEventLog[];

  @OneToMany(() => ApplicationDocument, (doc) => doc.application)
  documents: ApplicationDocument[];

  @OneToMany(() => UserConsent, (consent) => consent.application)
  userConsents: UserConsent[];

  @OneToMany(() => LoanOffer, (offer) => offer.application)
  offers: LoanOffer[];

  addresses: Address[];

  bankAccounts: BankAccount[];

  @OneToOne(() => IncomeVerification, (incomeVerification) => incomeVerification.application)
  incomeVerificaton: IncomeVerification;

  @Column({ name: 'lending_partner_id', type: 'int', nullable: true })
  lendingPartnerId?: number;

  @ManyToOne(() => LendingPartner, (partner) => partner.loanApplications)
  @JoinColumn({ name: 'lending_partner_id' })
  lendingPartner: LendingPartner;

  @OneToMany(() => ApplicationRemarks, (offer) => offer.application)
  remarks: ApplicationRemarks[];

  @Column({ name: 'dsa_agent_id', type: 'int', nullable: true })
  dsaAgentId: number;

  @ManyToOne(() => DsaAgent, { nullable: true })
  @JoinColumn({ name: 'dsa_agent_id', referencedColumnName: 'id' })
  dsaAgent: DsaAgent;

  businessDetail: BusinessDetail;

  @OneToMany(() => BusinessDirector, (director) => director.application)
  directors: BusinessDirector[];
}
