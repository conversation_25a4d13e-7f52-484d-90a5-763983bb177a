import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { firstValueFrom } from 'rxjs';
import { Address } from 'src/addresses/entity/address.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { ApplicationRemarks } from 'src/application-event-log/entity/application-remarks.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { LoanOffer, LoanOfferType } from 'src/loan-offer/entity/loan-offer.entity';
import { Message, parseQuery } from 'src/utils/response-util';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class LoanApplicationAdminService {
  private readonly logger = new Logger(LoanApplicationAdminService.name);
  private readonly authServiceBaseUrl: string;
  constructor(
    @InjectRepository(LoanApplication)
    private readonly loanAppRepo: Repository<LoanApplication>,
    @InjectRepository(Address)
    private readonly addressRepo: Repository<Address>,
    @InjectRepository(BankAccount)
    private readonly bankRepo: Repository<BankAccount>,
    @InjectRepository(LoanOffer)
    private readonly loanOfferRepo: Repository<LoanOffer>,
    private readonly httpService: HttpService,
    private readonly cls: ClsService,
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.authServiceBaseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
  }

  async getAll(query: any): Promise<Message> {
    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'createdAt',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;
    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }
    const whereClause = rawQuery ? parseQuery(rawQuery) : {};

    const [data, total] = await this.loanAppRepo.findAndCount({
      where: whereClause,
      relations: ['applicantDetail', 'product', 'status', 'lendingPartner'],
      order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
      skip,
      take: limitNum,
    });

    return {
      success: true,
      message: 'Loan applications fetched successfully',
      data,
      total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(total / limitNum),
    };
  }

  async getById(id: number): Promise<Message> {
    const record = await this.loanAppRepo.findOne({
      where: { id },
      relations: [
        'product',
        'loanPurpose',
        'status',
        'applicantDetail',
        'offers',
        'lendingPartner',
        'documents',
      ],
    });

    if (!record) {
      return {
        success: false,
        message: 'Loan application not found',
        error: 'Loan application not found',
      };
    }

    record.addresses = await this.addressRepo.find({
      where: {
        linkedEntityType: 'applicant_details',
        linkedEntityId: record.applicantDetail.id,
      },
    });

    record.bankAccounts = await this.bankRepo.find({
      where: {
        linkedEntityType: 'loan_applications',
        linkedEntityId: record.applicantDetail.id,
      },
    });

    return {
      success: true,
      message: 'Loan application fetched successfully',
      data: record,
    };
  }

  async approveLoanBySeniorCreditOfficer(id: number): Promise<Message> {
    this.logger.log('Hitting approveLoanBySeniorCreditOfficer Service Method ');
    const loan = await this.loanAppRepo.findOne({
      where: {
        id,
        subStatus: ApplicationSubStatus.DOCUMENTS_SIGNED,
        statusId: 2,
      },
    });

    if (!loan) {
      return {
        success: false,
        error: 'Loan application not found',
      };
    }
    const loanOfferRes = await this.loanOfferRepo.findOne({
      where: { applicationId: id, type: LoanOfferType.USER_ACCEPTED },
    });

    if (!loanOfferRes) {
      return {
        success: false,
        error: 'No  user accepted loanOffer Found for given application',
      };
    }

    loan.statusId = 3;
    const updated = await this.loanAppRepo.save(loan);

    this.logger.debug('loanOfferRes>> ', loanOfferRes);
    const loanTypeId = 1;
    const autoDebit = true;
    const sanctionAmount = +loanOfferRes.amount;
    const processingFees = +((loanOfferRes.amount * 3) / 100);
    const originalEmi = +loanOfferRes.emi;
    const originalTenure = +loanOfferRes.tenureInMonths;

    const createLoanPayload = {
      loanTypeId,
      autoDebit,
      sanctionAmount,
      processingFees,
      originalEmi,
      originalTenure,
    };

    const url = `${this.authServiceBaseUrl}/lms/api/loan`;
    const header = this.cls.get('header');

    this.logger.log('Calling lms create loan  API', createLoanPayload);

    const res = await firstValueFrom(
      this.httpService.post(url, createLoanPayload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `${header?.authorization}`,
        },
      })
    );

    if (!res.data?.success) {
      throw new Error('Invalid loan type ID');
    }

    this.logger.debug('Response from lms create-loan', res.data);

    const loanAccountNumberUpdateRes = await this.loanAppRepo.update(
      { id },
      { loanAccountNumber: res.data.data.loanAccountNumber }
    );
    if (!loanAccountNumberUpdateRes) {
      throw new Error('loanAccountNumber Update Failed');
    }

    return {
      success: true,
      message: 'Loan application approved successfully',
      data: {
        id: updated.id,
      },
    };
  }

  async getStatsCounts(): Promise<Message> {
    try {
      const roles = this.cls.get<string[]>('roles') || [];
      const isCreditOfficer = roles?.includes('credit-officer');
      const isSeniorCreditOfficer = roles?.includes('senior-credit-officer');

      let result:
        | {
            totalReview: string | number;
            pendingReview: string | number;
            rejected: string | number;
            approved: string | number;
          }
        | undefined;

      if (isCreditOfficer) {
        result = await this.loanAppRepo
          .createQueryBuilder('loan')
          .select([
            `COUNT(*) FILTER (
            WHERE 
              (loan.statusId = 1 AND loan.subStatus = 'VIDEO_KYC_SUCCESS') OR
              loan.statusId IN (2, 3, 5, 4)
          ) AS "totalReview"`,

            `COUNT(*) FILTER (
            WHERE loan.statusId = 1 AND loan.subStatus = 'VIDEO_KYC_SUCCESS'
          ) AS "pendingReview"`,

            `COUNT(*) FILTER (
            WHERE loan.statusId = 4
          ) AS "rejected"`,

            `COUNT(*) FILTER (
            WHERE loan.statusId IN (2, 3, 5)
          ) AS "approved"`,
          ])
          .getRawOne();
      } else if (isSeniorCreditOfficer) {
        result = await this.loanAppRepo
          .createQueryBuilder('loan')
          .select([
            `COUNT(*) FILTER (
            WHERE loan.statusId = 2 AND loan.subStatus = 'DOCUMENTS_SIGNED'
          ) AS "pendingReview"`,

            `COUNT(*) FILTER (
            WHERE loan.statusId = 6
          ) AS "rejected"`,

            `COUNT(*) FILTER (
            WHERE loan.statusId IN (3, 5)
          ) AS "approved"`,

            `COUNT(*) FILTER (
            WHERE 
              (loan.statusId = 2 AND loan.subStatus = 'DOCUMENTS_SIGNED') OR 
              loan.statusId IN (3, 5, 6)
          ) AS "totalReview"`,
          ])
          .getRawOne();
      } else {
        return {
          success: false,
          error: 'Unauthorized role',
        };
      }

      if (!result) {
        return {
          success: false,
          error: 'Failed to fetch review counts',
        };
      }
      return {
        success: true,
        message: 'Review counts fetched successfully',
        data: {
          totalReview: Number(result.totalReview),
          pendingReview: Number(result.pendingReview),
          rejected: Number(result.rejected),
          approved: Number(result.approved),
        },
      };
    } catch (error) {
      this.logger.error('Error fetching review counts', error);
      return {
        success: false,
        error: 'Failed to fetch review counts',
      };
    }
  }

  async rejectLoan(id: number, dto: { remarks: string }): Promise<Message> {
    this.logger.log('Hitting rejectLoan Service Method ');
    const roles = this.cls.get<string[]>('roles');

    const isCreditOfficer = roles.includes('credit-officer');
    const isSeniorCreditOfficer = roles.includes('senior-credit-officer');

    if (!isCreditOfficer && !isSeniorCreditOfficer) {
      return { success: false, error: 'Unauthorised Acess' };
    }

    let loan: LoanApplication | null = null;

    if (isCreditOfficer) {
      loan = await this.loanAppRepo.findOne({
        where: {
          id,
          subStatus: ApplicationSubStatus.VIDEO_KYC_SUCCESS,
          statusId: 1,
        },
        relations: ['applicantDetail'],
      });
    } else if (isSeniorCreditOfficer) {
      loan = await this.loanAppRepo.findOne({
        where: {
          id,
          subStatus: ApplicationSubStatus.DOCUMENTS_SIGNED,
          statusId: 2,
        },
        relations: ['applicantDetail'],
      });
    }

    if (!loan) {
      return {
        success: false,
        error: 'Loan application not found',
      };
    }

    let newStatus;
    if (isCreditOfficer) {
      newStatus = 4;
    } else newStatus = 6;

    await this.dataSource.transaction(async (manager) => {
      await manager.update(LoanApplication, id, {
        statusId: newStatus,
      });

      await manager.save(ApplicationRemarks, {
        applicationId: id,
        remarks: dto.remarks,
        action: isCreditOfficer ? 'CREDIT_OFFICER_REJECTION' : 'SENIOR_OFFICER_REJECTION',
        userRole: isCreditOfficer ? 'credit-officer' : 'senior-credit-officer',
      });

      await manager.save(ApplicationEventLog, {
        applicationId: id,
        eventType: isCreditOfficer
          ? ApplicationSubStatus.CREDIT_OFFICER_REJECTED
          : ApplicationSubStatus.SENIOR_OFFICER_REJECTED,
      });

      const frozenTillDate: Date = new Date();
      frozenTillDate.setDate(frozenTillDate.getDate() + 90);
      await manager.save(FrozenAccount, {
        pan: loan.applicantDetail.pan,
        userId: loan.userId,
        frozenTillDate,
        reason: `Application #${loan.id} Rejected`,
      });
    });

    return {
      success: true,
      message: 'Loan application rejected successfully',
      data: {},
    };
  }

  async getMisData(): Promise<Message> {
    try {
      const result:
        | {
            totalCreated: string | number;
            submitted: string | number;
            l1Approved: string | number;
            l2Approved: string | number;
            rejected: string | number;
            l1Pending: string | number;
            l2Pending: string | number;
            disbursed: string | number;
            approvalRate: string | number;
          }
        | undefined = await this.loanAppRepo
        .createQueryBuilder('loan')
        .select([
          `COUNT(*) AS "totalCreated",
          COUNT(*) FILTER (WHERE status_id = 2) AS "l1Approved",
          COUNT(*) FILTER (WHERE sub_status = 'DOCUMENT_SIGNED') AS "submitted",
          COUNT(*) FILTER (WHERE status_id = 3) AS "l2Approved",
          COUNT(*) FILTER (WHERE status_id in (4,6)) AS "rejected",
          COUNT(*) FILTER (WHERE status_id = 1 and sub_status = 'VIDEO_KYC_SUCCESS') AS "l1Pending",
          COUNT(*) FILTER (WHERE status_id = 5) AS "disbursed",
          COUNT(*) FILTER (WHERE status_id = 2 and sub_status = 'DOCUMENT_SIGNED') AS "l2Pending",
          ROUND(
          COUNT(*) FILTER (WHERE status_id = 3 AND sub_status = 'DOCUMENT_SIGNED') * 100.0
            / NULLIF(COUNT(*) FILTER (WHERE sub_status = 'DOCUMENT_SIGNED'), 0), 2
        ) AS approvalRate`,
        ])
        .cache(900 * 1000)
        .getRawOne();

      if (!result) {
        return {
          success: false,
          error: 'Failed to fetch MIS counts',
        };
      }

      return {
        success: true,
        message: 'Application MIS fetched successfully',
        data: {
          totalCreated: Number(result.totalCreated),
          submitted: Number(result.submitted),
          l1Approved: Number(result.l1Approved),
          l2Approved: Number(result.l2Approved),
          rejected: Number(result.rejected),
          l1Pending: Number(result.l1Pending),
          l2Pending: Number(result.l2Pending),
          disbursed: Number(result.disbursed),
          approvalRate: Number(result.approvalRate),
        },
      };
    } catch (error) {
      this.logger.error('Error fetching MIS counts', error);
      return {
        success: false,
        error: 'Error while fetching MIS',
      };
    }
  }
}
