import { Body, Controller, Get, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { LoanApplicationAdminService } from './loan-application-admin.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/role.decorator';

@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('credit-officer', 'senior-credit-officer', 'admin')
@Controller('admin/loan-application')
export class LoanApplicationAdminController {
  constructor(private readonly adminLoanAppService: LoanApplicationAdminService) {}

  @Get()
  async getAll(@Query() query: any) {
    return fixed_response(await this.adminLoanAppService.getAll(query));
  }

  @Get('mis')
  async getMisData() {
    return fixed_response(await this.adminLoanAppService.getMisData());
  }

  @Get('stats')
  @Roles('senior-credit-officer', 'credit-officer')
  async getLoanStats() {
    return fixed_response(await this.adminLoanAppService.getStatsCounts());
  }

  @Get(':id')
  async getOne(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.adminLoanAppService.getById(id));
  }

  @Post('approve-loan/:id')
  @Roles('senior-credit-officer')
  async approveLoanBySeniorCreditOfficer(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.adminLoanAppService.approveLoanBySeniorCreditOfficer(id));
  }

  @Post('reject-loan/:id')
  @Roles('senior-credit-officer', 'credit-officer')
  async rejectLoan(@Param('id', ParseIntPipe) id: number, @Body() dto: { remarks: string }) {
    return fixed_response(await this.adminLoanAppService.rejectLoan(id, dto));
  }
}
