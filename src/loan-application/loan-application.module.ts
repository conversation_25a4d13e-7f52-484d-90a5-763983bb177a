import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoanApplicationService } from './loan-application.service';
import { LoanApplicationController } from './loan-application.controller';
import { LoanApplication } from './entity/loan-application.entity';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import { Product } from 'src/product/entity/product.entity';
import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';
import { FrozenAccountModule } from 'src/frozen-accounts/frozen-account.module';
import { HttpModule } from '@nestjs/axios';
import { LoanApplicationAdminController } from './admin/loan-application-admin.controller';
import { LoanApplicationAdminService } from './admin/loan-application-admin.service';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { ApplicationDocument } from 'src/application-documents/entity/application-document.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import { LoanOffer } from 'src/loan-offer/entity/loan-offer.entity';
import { ApplicationKycMapping } from 'src/application-kyc-mapping/entity/application-kyc-mapping.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LoanApplication,
      ApplicantDetail,
      Product,
      LoanPurposeMaster,
      FrozenAccount,
      ApplicationDocument,
      Address,
      BankAccount,
      LoanOffer,
      ApplicationKycMapping,
    ]),
    FrozenAccountModule,
    HttpModule,
  ],
  controllers: [LoanApplicationController, LoanApplicationAdminController],
  providers: [LoanApplicationService, LoanApplicationAdminService, FileUploadService],
})
export class LoanApplicationModule {}
