import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, DataSource, MoreThanOrEqual } from 'typeorm';
import { ApplicationSubStatus, LoanApplication } from './entity/loan-application.entity';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import { CreateLoanApplicationDto, UpdateLoanApplicationDto } from './dto/loan-application.dto';
import { Product } from 'src/product/entity/product.entity';
import { Message } from 'src/utils/response-util';
import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';
import { ClsService } from 'nestjs-cls';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { ApplicationDocument } from 'src/application-documents/entity/application-document.entity';
import { AxiosResponse } from 'axios';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { ApplicationKycMapping } from 'src/application-kyc-mapping/entity/application-kyc-mapping.entity';
@Injectable()
export class LoanApplicationService {
  private readonly logger = new Logger(LoanApplicationService.name);
  constructor(
    @InjectRepository(LoanApplication)
    private readonly loanAppRepo: Repository<LoanApplication>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(ApplicantDetail)
    private readonly applicantRepo: Repository<ApplicantDetail>,
    @InjectRepository(LoanPurposeMaster)
    private readonly loanPurposeRepo: Repository<LoanPurposeMaster>,
    @InjectRepository(FrozenAccount)
    private readonly frozenRepo: Repository<FrozenAccount>,
    private readonly cls: ClsService,
    private readonly configService: ConfigService,
    private readonly fileUploadService: FileUploadService,
    private readonly httpService: HttpService,
    @InjectRepository(ApplicationDocument)
    private readonly appDocRepo: Repository<ApplicationDocument>,
    @InjectRepository(ApplicationKycMapping)
    private readonly appKycRepo: Repository<ApplicationKycMapping>,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  private readonly applicantCodeMap: Record<string, string> = {
    individual: 'IN',
    self_employed: 'SE',
    company: 'CO',
  };

  private getCurrentDatePart(): string {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    return `${year}${month}`;
  }

  private generateApplicationRefNum(
    productCode: string,
    applicantType: string,
    seqNum: number
  ): string {
    const datePart = this.getCurrentDatePart();
    const applicantCode = this.applicantCodeMap[applicantType.toLowerCase()] ?? 'XX';
    const seqPadded = seqNum.toString().padStart(5, '0');
    return `${productCode}-${applicantCode}-${datePart}-${seqPadded}`;
  }

  async createLoanApplication(dto: CreateLoanApplicationDto): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const existingPending = await this.loanAppRepo.findOne({
        where: { userId, statusId: 1 },
      });
      if (existingPending) {
        return {
          success: false,
          error: 'You already have a pending loan application',
        };
      }

      const isFrozen = await this.frozenRepo.findOne({ where: { userId } });
      if (isFrozen) {
        return {
          success: false,
          error: 'Your account is frozen. Loan application cannot be created.',
        };
      }

      const [product, loanPurpose] = await Promise.all([
        this.productRepo.findOne({ where: { id: dto.productId } }),
        this.loanPurposeRepo.findOne({ where: { id: dto.loanPurposeId } }),
      ]);

      if (!product) return { success: false, error: `Product with id ${dto.productId} not found` };
      if (!loanPurpose)
        return { success: false, error: `Loan purpose with id ${dto.loanPurposeId} not found` };

      let mobile = this.cls.get<string>('mobile');
      const authBaseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
      if (this.cls.get('dsaId')) {
        try {
          const { data } = await firstValueFrom(
            this.httpService.get(`${authBaseUrl}/auth/api/admin/search-user/${userId}`, {
              headers: {
                Authorization: this.cls.get('header')?.['authorization'],
              },
            })
          );
          mobile = data.data.mobileNumber;
        } catch (error) {
          this.logger.error('Failed to fetch user mobile:', error);
          return {
            success: false,
            error: 'Unable to fetch user data',
          };
        }
      }

      const kyc = this.httpService.get(`${authBaseUrl}/kyc/api/user-details`, {
        headers: {
          Authorization: this.cls.get('header')?.['authorization'],
        },
      });
      interface KycUserDetails {
        pan: string;
        name: string;
        dob: string;
        gender: string;
        verifiedKyc: any;
      }
      interface KycResponse {
        data: {
          data: KycUserDetails;
        };
      }
      const kycResponse = (await firstValueFrom(kyc)) as KycResponse;
      const { pan, name, dob, gender, verifiedKyc } = kycResponse.data.data;
      let formattedDob: string | null = null;
      if (dob) {
        const parts: string[] = dob.split('-');
        if (parts.length === 3) {
          formattedDob = `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
      }
      let entityType = '';
      if (pan && /^[A-Z]{3}P/.test(pan.toUpperCase())) {
        entityType = 'INDIVIDUAL';
      }
      const datePart = this.getCurrentDatePart();
      const applicantCode = this.applicantCodeMap[entityType.toLowerCase()] ?? 'XX';
      const refNumPrefix = `${product.productCode}-${applicantCode}-${datePart}-`;

      const existingCount = await this.loanAppRepo.count({
        where: { applicationRefNum: Like(`${refNumPrefix}%`) },
      });

      const nextSeqNum = existingCount + 1;
      const applicationRefNum = this.generateApplicationRefNum(
        product.productCode,
        entityType,
        nextSeqNum
      );

      const loanApp = this.loanAppRepo.create({
        productId: product.id,
        userId,
        loanPurposeId: loanPurpose.id,
        applicantType: entityType,
        statusId: 1,
        applicationRefNum,
        subStatus: ApplicationSubStatus.PERSONAL_INFO,
        lendingPartnerId: 1, // TODO: put a logic to map lending partner
      });

      const appId = await this.dataSource.transaction(async (manager) => {
        const app = await manager.save(LoanApplication, loanApp);

        const applicantDetails = this.applicantRepo.create({
          pan: pan,
          name,
          gender,
          mobile,
          ...(formattedDob && { dob: formattedDob }),
          application: app,
          dependentsCount: dto.dependentsCount,
          education: dto.education,
          maritalStatus: dto.maritalStatus,
          salaryDisbursementMode: 'BANK',
        });

        const aadhaarKycId = verifiedKyc?.aadhaar;
        const panKycId = verifiedKyc?.pan;
        const aadhaarPanLinkKycId = verifiedKyc?.['aadhaar-pan-link'];

        if (aadhaarKycId && panKycId && aadhaarPanLinkKycId) {
          applicantDetails.kycVerified = true;
        }

        await manager.save(ApplicantDetail, applicantDetails);
        await manager.save(ApplicationEventLog, {
          applicationId: app.id,
          eventType: ApplicationSubStatus.PERSONAL_INFO,
        });
        if (verifiedKyc && typeof verifiedKyc === 'object') {
          const kycMappings = Object.entries(verifiedKyc).map(([type, kycId]) =>
            this.appKycRepo.create({
              applicationId: app.id,
              kycId: Number(kycId),
              type,
            })
          );

          await manager.save(ApplicationKycMapping, kycMappings);
        }
        return app.id;
      });

      return {
        success: true,
        data: { id: appId },
        message: 'Loan application created',
      };
    } catch (error) {
      this.logger.error('Error creating loan application:', error);
      return { success: false, error: error.message ?? 'Internal server error' };
    }
  }

  async getBasicDetailsById(applicationId: number): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const result = await this.applicantRepo
        .createQueryBuilder('applicant')
        .innerJoin(LoanApplication, 'app', 'app.id = applicant.application_id')
        .where('applicant.application_id = :applicationId', { applicationId })
        .andWhere('app.user_id = :userId', { userId })
        .select(['applicant.name AS name', 'applicant.pan AS pan', 'applicant.mobile AS mobile'])
        .getRawOne();

      if (!result) {
        return {
          success: false,
          error: 'Unauthorized or no such application found.',
        };
      }

      return {
        success: true,
        data: {
          name: result.name,
          pan: result.pan,
          mobile: result.mobile,
        },
      };
    } catch (error) {
      this.logger.error('Error in getBasicDetailsById:', error);
      return {
        success: false,
        error: 'Internal server error',
      };
    }
  }

  async getPendingApplication(): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const lastApplication = await this.loanAppRepo.findOne({
        where: {
          userId,
          statusId: 1,
        },
        order: { createdAt: 'DESC' },
      });

      if (!lastApplication) {
        const baseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
        const response = await firstValueFrom(
          this.httpService.get(`${baseUrl}/auth/api/profile`, {
            headers: {
              Authorization: this.cls.get('header')['authorization'],
            },
          })
        );
        const { firstName, lastName, gender, dob } = response.data.data;
        const fullName =
          firstName || lastName ? `${firstName ?? ''} ${lastName ?? ''}`.trim() : null;
        return {
          success: true,
          data: {
            name: fullName,
            gender,
            dob,
          },
          message: 'Profile data fetched from auth service',
        };
      }

      const applicant = await this.applicantRepo.findOne({
        where: { application: { id: lastApplication.id } },
      });

      return {
        success: true,
        data: {
          loanPurposeId: lastApplication.loanPurposeId,
          ...applicant,
        },
        message: 'Loan application details fetched successfully',
      };
    } catch (error) {
      this.logger.error('Error in getPendingApplication:', error);
      return {
        success: false,
        error: 'Internal server error',
      };
    }
  }

  async updateLoanApplication(
    applicationId: number,
    dto: UpdateLoanApplicationDto
  ): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const existingApp = await this.loanAppRepo.findOne({
        where: { id: applicationId, userId },
        relations: ['applicantDetail'],
      });

      if (!existingApp || !existingApp.applicantDetail) {
        return {
          success: false,
          error: 'Loan application or applicant details not found or unauthorized',
        };
      }
      const isFrozen = await this.frozenRepo.findOne({ where: { userId } });
      if (isFrozen) {
        return {
          success: false,
          error: 'Your account is frozen. Loan application cannot be updated.',
        };
      }

      const loanPurpose = await this.loanPurposeRepo.findOne({
        where: { id: dto.loanPurposeId },
      });

      if (!loanPurpose) {
        return {
          success: false,
          error: `Loan purpose with id ${dto.loanPurposeId} not found`,
        };
      }

      this.loanAppRepo.merge(existingApp, {
        loanPurposeId: dto.loanPurposeId,
      });
      await this.loanAppRepo.save(existingApp);

      const applicant = existingApp.applicantDetail;
      if (applicant) {
        this.applicantRepo.merge(applicant, {
          dependentsCount: dto.dependentsCount,
          education: dto.education,
          maritalStatus: dto.maritalStatus,
        });
        await this.applicantRepo.save(applicant);
      }

      return {
        success: true,
        message: 'Loan application updated successfully',
      };
    } catch (error) {
      this.logger.error('Error in updateLoanApplication:', error);
      return {
        success: false,
        error: error.message || 'Internal server error',
      };
    }
  }

  async getLatestApplication(): Promise<Message> {
    const userId = this.cls.get<number>('userId');
    const today = new Date();
    const frozenAccount = await this.frozenRepo.findOne({
      where: { userId, frozenTillDate: MoreThanOrEqual(today) },
    });

    if (frozenAccount) {
      return {
        success: true,
        data: { type: 'frozenAccount', value: frozenAccount },
        message: 'Frozen account exists',
      };
    }

    const cutoff = new Date();
    cutoff.setMonth(cutoff.getMonth() - 3);
    const record = await this.loanAppRepo.findOne({
      where: { userId },
      order: { createdAt: 'DESC' },
      relations: {
        status: true,
      },
    });

    if (!record || record.createdAt < cutoff) {
      return {
        success: false,
        error: 'No recent application',
      };
    }

    return {
      success: true,
      message: 'Latest application fetched successfully',
      data: { type: 'loan', value: record },
    };
  }

  async createDocument(): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const application = await this.loanAppRepo.findOne({
        where: { statusId: 2, userId, subStatus: ApplicationSubStatus.NACH_MANDATE },
      });
      if (!application) {
        return { success: false, error: 'No loan application found with statusId = 2' };
      }

      // load existing docs
      const existingDocs = await this.appDocRepo.find({
        where: { applicationId: application.id },
      });
      const existingTypes = new Set(existingDocs.map((d) => d.docType));

      // prepare endpoint
      const rawUrl = this.configService.getOrThrow<string>('DOCUMENT_SERVICE_URL');
      if (!rawUrl) {
        return { success: false, error: 'DOCUMENT_SERVICE_URL not configured' };
      }
      const url = `${rawUrl.replace(/\/$/, '')}/generate/KFS`;

      // define all possible jobs
      const jobs = [
        {
          payload: {
            loanAccountNo: '1111',
            loanAmount: '2000',
            interestRateType: '45',
            numberOfEPIs: '20',
            epiAmount: '34',
            template: 'lala',
          },
          docType: 'KFS' as const,
          folder: 'kfs-documents',
          fileName: `kfs-${application.id}.pdf`,
        },
        {
          payload: {
            template: 'loanAgreement',
            city: 'Mumbai',
            without: 'without',
            date: '01 January 2025',
            lenderAddress: '501, ABC Business Center, Andheri East, Mumbai - 400059',
            cin: 'U12345MH2010PTC123456',
            rbiCor: 'NBFC123456789',
            borrowerName: 'Ravi Kumar',
            fatherName: 'Mahesh Kumar',
            borrowerAddress: 'Flat 402, Sunshine Residency, Goregaon West, Mumbai – 400104',
            pan: '**********',
            aadhaar: '************',
            loanAmount: '1,50,000',
            loanPurpose: 'Education Fees',
            tenureMonths: 12,
            interestRate: 14.5,
            repaymentMode: 'EMI through Auto-debit',
            disbursementDate: '01 February 2025',
            repaymentFrequency: '12 monthly',
            installmentAmount: '13,375',
            firstInstallmentDate: '01 March 2025',
            paymentMethod: 'bank auto-debit',
            penalInterest: 2,
            graceDays: 7,
            partnerEntityName: 'XYZ Fintech Services Pvt Ltd',
            prepaymentCharge: 2.5,
            jurisdictionCity: 'Mumbai',
          },
          docType: 'loanAgreement' as const,
          folder: 'loan-agreements',
          fileName: `loan-agreement-${application.id}.pdf`,
        },
        {
          payload: {
            date: '28 July 2025',
            template: 'sanctionLetter',
            borrowerFullName: 'Ravi Kumar',
            borrowerAddress: 'Flat 402, Sunshine Residency, Goregaon West, Mumbai – 400104',
            loanAmount: '1,50,000',
            tenure: 12,
            interestRate: 14.5,
            processingFee: '1,500',
            repaymentFrequency: 'Monthly',
            firstRepaymentDate: '01 September 2025',
            repaymentMode: 'Auto-debit from savings account',
            security: 'Personal Guarantee',
            partnerEntity: 'XYZ Fintech Services Pvt Ltd',
          },
          docType: 'sanctionLetter' as const,
          folder: 'sanction-letters',
          fileName: `sanction-letter-${application.id}.pdf`,
        },
      ];

      // only generate docs that don't already exist
      const toRun = jobs.filter((job) => !existingTypes.has(job.docType));
      const created: Array<{ id: number; docType: string; url: string }> = [];

      for (const job of toRun) {
        try {
          const resp: AxiosResponse<ArrayBuffer> = await firstValueFrom(
            this.httpService.post<ArrayBuffer>(url, job.payload, { responseType: 'arraybuffer' })
          );
          const buffer = Buffer.from(resp.data);

          const upload = await this.fileUploadService.uploadBuffer(
            buffer,
            job.folder,
            job.fileName,
            'application/pdf'
          );

          const entity = this.appDocRepo.create({
            applicationId: application.id,
            docType: job.docType,
            url: upload.data.key,
            docName: job.fileName,
          });
          await this.appDocRepo.save(entity);

          created.push({ id: entity.id, docType: job.docType, url: upload.data.key });
        } catch (e: any) {
          this.logger.error(`Failed to generate ${job.docType}: ${e.message}`);
        }
      }

      // now sign URLs for all docs
      const bucket = this.configService.getOrThrow<string>('AWS_S3_BUCKET');

      const existingWithUrls = await Promise.all(
        existingDocs.map(async (d) => ({
          id: d.id,
          docType: d.docType,
          url: d.url,
          signedUrl: await this.fileUploadService.generateSignedUrl(bucket, String(d.url)),
        }))
      );

      const createdWithUrls = await Promise.all(
        created.map(async (c) => ({
          id: c.id,
          docType: c.docType,
          url: c.url,
          signedUrl: await this.fileUploadService.generateSignedUrl(bucket, String(c.url)),
        }))
      );

      const allDocs = [...existingWithUrls, ...createdWithUrls];
      return { success: true, data: allDocs };
    } catch (err: any) {
      this.logger.error(`Unexpected error in createDocument: ${err.message}`, err.stack);
      return { success: false, error: 'Internal server error' };
    }
  }
}
