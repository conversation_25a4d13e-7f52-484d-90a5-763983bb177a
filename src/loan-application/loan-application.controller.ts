import { Controller, Post, Body, UseGuards, Get, Param, ParseIntPipe, Put } from '@nestjs/common';
import { LoanApplicationService } from './loan-application.service';
import { CreateLoanApplicationDto, UpdateLoanApplicationDto } from './dto/loan-application.dto';
import { fixed_response } from 'src/utils/response-util';
import { FrozenAccountDto } from 'src/frozen-accounts/dto/frozen-account.dto';
import { FrozenAccountService } from 'src/frozen-accounts/frozen-account.service';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('loan-application')
export class LoanApplicationController {
  constructor(
    private readonly service: LoanApplicationService,
    private readonly frozenAccountService: FrozenAccountService
  ) {}

  @Post()
  async create(@Body() dto: CreateLoanApplicationDto) {
    return fixed_response(await this.service.createLoanApplication(dto));
  }

  @Post('salary-mode-verification')
  async verifySalaryMode(@Body() dto: FrozenAccountDto) {
    return fixed_response(await this.frozenAccountService.verifySalaryMode(dto));
  }

  @Get('pending')
  async getPendingApplication() {
    return fixed_response(await this.service.getPendingApplication());
  }

  @Put(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateLoanApplicationDto) {
    return fixed_response(await this.service.updateLoanApplication(id, dto));
  }

  @Get('latest')
  async getLatestApplication() {
    return fixed_response(await this.service.getLatestApplication());
  }

  @Get(':id')
  async getBasicDetailsById(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.service.getBasicDetailsById(id));
  }

  @Post('create-document')
  async createDocument() {
    const result = await this.service.createDocument();
    return fixed_response(result);
  }
}
