import { IsEnum, IsInt } from 'class-validator';
import { MaritalStatus } from 'src/applicant-details/entity/applicant-details.entity';
import { EducationalQualification } from 'src/common/enums/education.enum';

export class CreateLoanApplicationDto {
  @IsInt()
  loanPurposeId: number;

  @IsInt()
  dependentsCount: number;

  @IsEnum(EducationalQualification)
  education: EducationalQualification;

  @IsEnum(MaritalStatus, { message: 'Invalid marital status' })
  maritalStatus: MaritalStatus;

  @IsInt()
  productId: number;
}

export class UpdateLoanApplicationDto {
  @IsInt()
  loanPurposeId: number;

  @IsInt()
  dependentsCount: number;

  @IsEnum(EducationalQualification)
  education: EducationalQualification;

  @IsEnum(MaritalStatus, { message: 'Invalid marital status' })
  maritalStatus: MaritalStatus;
}
