import { BadRequestException, ValidationError, ValidationPipe } from '@nestjs/common';

export class CustomValidationPipe extends ValidationPipe {
  constructor() {
    super({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const formattedErrors = errors.map((error) => ({
          field: error.property,
          message: Object.values(error.constraints ?? {}),
        }));
        return new BadRequestException({
          success: false,
          status: 400,
          error: 'Bad Request',
          errors: formattedErrors,
        });
      },
    });
  }
}
