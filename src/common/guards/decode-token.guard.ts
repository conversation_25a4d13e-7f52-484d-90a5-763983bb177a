import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Request } from 'express';
import { decode } from 'jsonwebtoken';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class DecodeTokenGuard implements CanActivate {
  constructor(private readonly cls: ClsService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers['authorization'];

    if (!authHeader?.startsWith('Bearer ')) {
      throw new UnauthorizedException('Missing or invalid Authorization header');
    }

    interface JwtPayload {
      sub: string;
      mobile?: string;
      roles?: string[];
    }

    const token = authHeader.split(' ')[1];
    const decoded = decode(token) as JwtPayload;

    if (!decoded || typeof decoded !== 'object' || !('sub' in decoded)) {
      throw new UnauthorizedException('Invalid token payload');
    }

    (request as any).user = decoded;

    const authActingUserHeader = request.headers['X-ACTING-USER'];
    if (
      authActingUserHeader &&
      typeof authActingUserHeader === 'string' &&
      authActingUserHeader.trim() !== '' &&
      !isNaN(Number(authActingUserHeader))
    ) {
      this.cls.set('userId', authActingUserHeader);
      this.cls.set('dsaId', decoded.sub);
    } else {
      this.cls.set('userId', decoded.sub);
    }
    this.cls.set('mobile', decoded.mobile);
    this.cls.set('roles', decoded.roles);
    return true;
  }
}
