import { ClsServiceManager } from 'nestjs-cls';
import {
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BaseEntity as TypeORMBaseEntity,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';

export abstract class CustomBaseEntity extends TypeORMBaseEntity {
  @CreateDateColumn({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy?: number;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: number;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt?: Date;

  // This will be executed before every insert to set createdBy and updatedBy
  @BeforeInsert()
  setCreatedBy() {
    const userId: number =
      ClsServiceManager.getClsService().get('dsaId') ??
      ClsServiceManager.getClsService().get('userId'); // Fetch the current user ID
    if (userId) {
      this.createdBy = userId;
      this.updatedBy = userId;
    }
  }

  // This will be executed before every update to set updatedBy
  @BeforeUpdate()
  setUpdatedBy() {
    const userId: number =
      ClsServiceManager.getClsService().get('dsaId') ??
      ClsServiceManager.getClsService().get('userId'); // Fetch the current user ID
    if (userId) {
      this.updatedBy = userId;
    }
  }
}
