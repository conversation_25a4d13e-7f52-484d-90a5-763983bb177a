// user_to_partner_customer.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, Index, OneToMany } from 'typeorm';
import { CustomBaseEntity } from './base.entity';
import { CibilQuestionLog } from 'src/bureau/entity/cibil-question-log.entity';

@Entity({ name: 'user_to_partner_customer' })
@Index(['userId', 'partnerCustomerId']) // composite index for “latest by user” queries
export class UserToPartnerCustomer extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ name: 'user_id' })
  userId: number;

  @Index({ unique: true })
  @Column({
    name: 'partner_customer_id',
    type: 'uuid',
    default: () => 'uuid_generate_v4()',
  })
  partnerCustomerId: string;

  // Inverse relationship
  @OneToMany(() => CibilQuestionLog, (log) => log.userMapping)
  questionLogs: CibilQuestionLog[];

  @Column({ name: 'iv_status', nullable: true })
  ivStatus: string;
}
