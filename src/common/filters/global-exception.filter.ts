import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    if (exception instanceof BadRequestException) {
      const res = exception.getResponse();

      // If it's already formatted, return it directly
      if (typeof res === 'object' && res !== null && 'errors' in res) {
        return response.status(400).json(res);
      }
    }
    let error = 'An unknown error occurred';
    let status = HttpStatus.INTERNAL_SERVER_ERROR;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      error = exception.message;
    } else if (exception instanceof Error) {
      error = exception.message;
    }

    response.status(status).json({
      success: false,
      error,
      path: request.url,
      timestamp: new Date().toISOString(),
    });
  }
}
