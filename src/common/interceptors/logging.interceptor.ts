import { Injectable, NestInterceptor, Execution<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { Request } from 'express';
import { Observable, tap } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request: Request = context.switchToHttp().getRequest();

    const method = request.method;
    const url = request.originalUrl;
    const controller = context.getClass().name;
    const handler = context.getHandler().name;
    const requestId = randomUUID();
    this.logger.log(`[${requestId}]->[${method}] ${url} → ${controller}.${handler} called`);

    return next.handle().pipe(
      tap(() => {
        this.logger.log(`[${requestId}]->[${method}] ${url} → ${controller}.${handler} completed`);
      })
    );
  }
}
