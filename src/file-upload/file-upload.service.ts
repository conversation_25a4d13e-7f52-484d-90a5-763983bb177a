import { Injectable, Logger } from '@nestjs/common';
import { createReadStream } from 'fs';
import { v4 as uuid } from 'uuid';
import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { Message } from 'src/utils/response-util';
import { ConfigService } from '@nestjs/config';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as fs from 'fs/promises';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private readonly config: {
    s3Bucket: string;
    s3AccessKeyId: string;
    s3SecretAccessKey: string;
    s3Region?: string;
  };
  s3: S3Client;

  constructor(
    private readonly configService: ConfigService,
    private readonly clsService: ClsService
  ) {
    this.config = {
      s3Bucket: this.configService.getOrThrow<string>('AWS_S3_BUCKET'),
      s3AccessKeyId: this.configService.getOrThrow<string>('S3_ACCESS_KEY_ID'),
      s3SecretAccessKey: this.configService.getOrThrow<string>('S3_SECRET_ACCESS_KEY'),
      s3Region: this.configService.getOrThrow<string>('S3_REGION'),
    };

    this.s3 = new S3Client({
      region: this.config.s3Region,
      credentials: {
        accessKeyId: this.config.s3AccessKeyId,
        secretAccessKey: this.config.s3SecretAccessKey,
      },
    });
  }

  async uploadFile(file: Express.Multer.File): Promise<Message> {
    this.logger.log('hitting uploadFile service');

    if (!file) throw new Error('No file received');

    const result = await this.s3Upload(file);

    const signedUrl = await this.generateSignedUrl(this.config.s3Bucket, String(result.Key));
    this.logger.debug('signedUrl >>', signedUrl);
    return {
      success: true,
      data: {
        key: result.Key,
        signedUrl,
      },
    };
  }

  async s3Upload(file: Express.Multer.File) {
    const params = {
      Bucket: this.config.s3Bucket,
      Key: `users/${this.clsService.get<number>('userId')}/${uuid()}-${file.originalname}`,
      Body: createReadStream(file.path),
      ContentType: file.mimetype,
      ContentDisposition: 'inline',
      CreateBucketConfiguration: {
        LocationConstraint: this.config.s3Region,
      },
    };

    const upload = new Upload({
      client: this.s3,
      params: params,
    });

    const result = await upload.done();
    await fs.unlink(file.path);
    this.logger.log('temp  directory  cleaned');

    return result;
  }

  async generateSignedUrl(bucketName: string, objectKey: string) {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: objectKey,
    });

    const signedUrl = await getSignedUrl(this.s3, command, { expiresIn: 30 * 60 });

    return signedUrl;
  }

  async uploadBuffer(
    buffer: Buffer,
    folder: string,
    filename: string,
    mimeType: string
  ): Promise<Message> {
    const userId = this.clsService.get<number>('userId');
    const key = `${folder}/${userId}/${uuid()}-${filename}`;

    const upload = new Upload({
      client: this.s3,
      params: {
        Bucket: this.config.s3Bucket,
        Key: key,
        Body: buffer,
        ContentType: mimeType,
        ACL: 'private',
      },
    });

    const result: any = await upload.done();
    this.logger.log(`Buffer uploaded to S3 as ${key}`);
    return {
      success: true,
      data: {
        key: result.Key,
      },
    };
  }
}
