import { Controller, Post, Body, Get, UseGuards } from '@nestjs/common';
import { ApplicationDocumentsService } from './application-documents.service';
import { CreateApplicationDocumentDto } from './dto/create-application-document.dto';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('application-documents')
export class ApplicationDocumentsController {
  constructor(private readonly docsService: ApplicationDocumentsService) {}

  @Post()
  async create(@Body() dto: CreateApplicationDocumentDto) {
    const result = await this.docsService.createDocument(dto);
    return fixed_response(result);
  }

  @Get()
  async getAll() {
    return fixed_response(await this.docsService.getAllDocuments());
  }
}
