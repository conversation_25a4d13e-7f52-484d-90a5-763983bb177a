import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';

@Entity({ name: 'application_documents' })
@Index('IDX_APP_DOC_APP_ID', ['applicationId'])
export class ApplicationDocument extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @ManyToOne(() => LoanApplication, (app) => app.documents)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Column({ name: 'doc_type', type: 'varchar', length: 255 })
  docType: string;

  @Column({ name: 'url', type: 'varchar', length: 500 })
  url: string;

  @Column({ name: 'doc_name', type: 'varchar', length: 255, nullable: true })
  docName?: string;

  @Column({ name: 'confirmation_datetime', type: 'timestamp', nullable: true })
  confirmationDatetime?: Date;

  @Column({ name: 'signing_datetime', type: 'timestamp', nullable: true })
  signingDatetime?: Date;

  @Column({ name: 'sign_type', type: 'varchar', length: 100, nullable: true })
  signType?: string;
}
