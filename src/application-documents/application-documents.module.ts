import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationDocument } from './entity/application-document.entity';
import { ApplicationDocumentsService } from './application-documents.service';
import { ApplicationDocumentsController } from './application-documents.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationDocument])],
  providers: [ApplicationDocumentsService],
  controllers: [ApplicationDocumentsController],
  exports: [ApplicationDocumentsService],
})
export class ApplicationDocumentsModule {}
