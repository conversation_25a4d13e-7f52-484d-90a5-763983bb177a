import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationDocument } from './entity/application-document.entity';
import { CreateApplicationDocumentDto } from './dto/create-application-document.dto';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class ApplicationDocumentsService {
  constructor(
    @InjectRepository(ApplicationDocument)
    private readonly docRepo: Repository<ApplicationDocument>,
    private readonly clsService: ClsService
  ) {}

  async createDocument(dto: CreateApplicationDocumentDto): Promise<Message> {
    try {
      const doc = this.docRepo.create(dto);
      const saved = await this.docRepo.save(doc);
      return { success: true, data: saved.id };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create document',
      };
    }
  }

  async getAllDocuments(): Promise<Message> {
    try {
      const userId = this.clsService.get<number>('userId');
      const docs = await this.docRepo
        .createQueryBuilder('doc')
        .innerJoin('doc.application', 'app')
        .addSelect(['app.userId', 'app.statusId'])
        .where('app.userId = :userId', { userId })
        .andWhere('app.statusId = :statusId', { statusId: 2 })
        .orderBy('doc.id', 'DESC')
        .getMany();

      return { success: true, data: docs, message: 'get documents successfully' };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch documents',
      };
    }
  }
}
