import { IsInt, IsString, IsOptional, IsDateString } from 'class-validator';

export class CreateApplicationDocumentDto {
  @IsInt()
  applicationId: number;

  @IsString()
  docType: string;

  @IsString()
  url: string;

  @IsOptional()
  @IsString()
  docName?: string;

  @IsOptional()
  @IsDateString()
  confirmationDatetime?: string;

  @IsOptional()
  @IsDateString()
  signingDatetime?: string;

  @IsOptional()
  @IsString()
  signType?: string;
}
