import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from 'src/product/entity/product.entity';

@Entity({ name: 'leads' })
export class Lead extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ length: 6 })
  pincode: string;

  @Column({ name: 'loan_type_inquired', type: 'int' })
  loanTypeInquired: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'loan_type_inquired', referencedColumnName: 'id' })
  product: Product;
}
