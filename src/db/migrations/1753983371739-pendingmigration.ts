import { MigrationInterface, QueryRunner } from "typeorm";

export class Pendingmigration1753983371739 implements MigrationInterface {
    name = 'Pendingmigration1753983371739'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."encrypted_data" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "encrypted_value" text NOT NULL, "iv" character varying(32) NOT NULL, CONSTRAINT "PK_9d82f126b1cc08d45ebe948cc9c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "los"."application_kyc_mapping" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "kyc_id" integer NOT NULL, "type" character varying(100) NOT NULL, CONSTRAINT "PK_ca1896a94c9533febc8c1d0a7b6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`CREATE INDEX "IDX_3ab4e49113f0f98076c9303ffc" ON "los"."loan_applications" ("sub_status") `);
        await queryRunner.query(`ALTER TABLE "los"."application_kyc_mapping" ADD CONSTRAINT "FK_c832c9070db9dde18344cb24ce8" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."application_kyc_mapping" DROP CONSTRAINT "FK_c832c9070db9dde18344cb24ce8"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_3ab4e49113f0f98076c9303ffc"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "created_at"`);
        await queryRunner.query(`DROP TABLE "los"."application_kyc_mapping"`);
        await queryRunner.query(`DROP TABLE "los"."encrypted_data"`);
    }

}
