import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigrations1755760413341 implements MigrationInterface {
    name = 'PendingMigrations1755760413341'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."business_directors" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "mobile" character varying, "email" character varying, "name" character varying NOT NULL, "pan" character varying, "din" character varying NOT NULL, CONSTRAINT "PK_980a0b84872969ad419df6cf15f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "los"."dsa_agents" ADD "user_id" integer`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" ADD "dsa_agent_id" integer`);
        await queryRunner.query(`ALTER TABLE "los"."business_details" ADD "contact_number" character varying(10) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."business_directors" ADD CONSTRAINT "FK_bbcb9f132f77098b36b5c938ac0" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" ADD CONSTRAINT "FK_0a198363ddfc5a58437545ccf69" FOREIGN KEY ("dsa_agent_id") REFERENCES "los"."dsa_agents"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" DROP CONSTRAINT "FK_0a198363ddfc5a58437545ccf69"`);
        await queryRunner.query(`ALTER TABLE "los"."business_directors" DROP CONSTRAINT "FK_bbcb9f132f77098b36b5c938ac0"`);
        await queryRunner.query(`ALTER TABLE "los"."business_details" DROP COLUMN "contact_number"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" DROP COLUMN "dsa_agent_id"`);
        await queryRunner.query(`ALTER TABLE "los"."dsa_agents" DROP COLUMN "user_id"`);
        await queryRunner.query(`DROP TABLE "los"."business_directors"`);
    }

}
