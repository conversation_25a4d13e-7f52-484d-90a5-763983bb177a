import { MigrationInterface, QueryRunner } from 'typeorm';

export class DsaManagement1755059256319 implements MigrationInterface {
  name = 'DsaManagement1755059256319';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "los"."dsa_master" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "agency_name" character varying(255) NOT NULL, "contact_number" character varying(12) NOT NULL, "contact_person_name" character varying(255) NOT NULL, "email" character varying(255) NOT NULL, "company_pan" character varying(10) NOT NULL, "operation_region" character varying(255) NOT NULL, "dsa_code" character varying(6) NOT NULL, CONSTRAINT "UQ_f7538138c372a79fed01ed521b0" UNIQUE ("dsa_code"), CONSTRAINT "PK_b378efdc22bef7661c1ee655ffb" PRIMARY KEY ("id"))`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "los"."dsa_master"`);
  }
}
