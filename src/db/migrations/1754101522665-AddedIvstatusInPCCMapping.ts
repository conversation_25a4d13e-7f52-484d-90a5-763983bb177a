import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedIvstatusInPCCMapping1754101522665 implements MigrationInterface {
    name = 'AddedIvstatusInPCCMapping1754101522665'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "los"."IDX_USER_USER_ID"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_USER_CREATED_AT"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_32ef7b5ba363a8b429c24dcd33"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ADD "iv_status" character varying`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" DROP CONSTRAINT "UQ_28af845b31c6731110a210e5fa0"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" DROP COLUMN "partner_customer_id"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ADD "partner_customer_id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`CREATE INDEX "IDX_8cf9d6a63b9e0bd253420a7735" ON "los"."applicant_details" ("application_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_675fc88c789366e3f362ebbccb" ON "los"."loan_applications" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e33734ddb7113e373e5191de66" ON "los"."loan_applications" ("user_id", "created_at") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_28af845b31c6731110a210e5fa" ON "los"."user_to_partner_customer" ("partner_customer_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_d879b0a9546c5e2d60b8e583b7" ON "los"."user_to_partner_customer" ("user_id", "partner_customer_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_0ce1edb67ff64dae6b7faaed5e" ON "los"."cibil_question_log" ("application_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5bb559b776b6508831c33f41d2" ON "los"."cibil_question_log" ("challenge_guid") `);
        await queryRunner.query(`CREATE INDEX "idx_address_entity_lookup" ON "los"."addresses" ("entity_type", "entity_id", "type") `);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`DROP INDEX "los"."idx_address_entity_lookup"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_5bb559b776b6508831c33f41d2"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_0ce1edb67ff64dae6b7faaed5e"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_d879b0a9546c5e2d60b8e583b7"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_28af845b31c6731110a210e5fa"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_e33734ddb7113e373e5191de66"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_675fc88c789366e3f362ebbccb"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_8cf9d6a63b9e0bd253420a7735"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" DROP COLUMN "partner_customer_id"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ADD "partner_customer_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ADD CONSTRAINT "UQ_28af845b31c6731110a210e5fa0" UNIQUE ("partner_customer_id")`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" DROP COLUMN "iv_status"`);
        await queryRunner.query(`CREATE INDEX "IDX_32ef7b5ba363a8b429c24dcd33" ON "los"."addresses" ("entity_id", "entity_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_USER_CREATED_AT" ON "los"."loan_applications" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_USER_USER_ID" ON "los"."loan_applications" ("user_id") `);
    }

}
