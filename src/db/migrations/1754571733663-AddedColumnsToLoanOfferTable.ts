import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumnsToLoanOfferTable1754571733663 implements MigrationInterface {
    name = 'AddedColumnsToLoanOfferTable1754571733663'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "share_percentage"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD "final_eligibility" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD "metadata" jsonb`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD CONSTRAINT "CHK_fe392f922847536dd6c7afa07c" CHECK ("tenure_in_months" > 0)`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD CONSTRAINT "CHK_ba61673f4b873367f852c886a6" CHECK ("roi" > 0)`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD CONSTRAINT "CHK_1ae69245a180244daf210c84e7" CHECK ("amount" > 0)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP CONSTRAINT "CHK_1ae69245a180244daf210c84e7"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP CONSTRAINT "CHK_ba61673f4b873367f852c886a6"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP CONSTRAINT "CHK_fe392f922847536dd6c7afa07c"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP COLUMN "metadata"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP COLUMN "final_eligibility"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "share_percentage" numeric(5,2) NOT NULL DEFAULT '0'`);
    }

}
