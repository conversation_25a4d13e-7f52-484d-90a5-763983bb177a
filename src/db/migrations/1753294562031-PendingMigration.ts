import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigration1753294562031 implements MigrationInterface {
    name = 'PendingMigration1753294562031'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" ALTER COLUMN "mobile" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_28af845b31c6731110a210e5fa"`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ALTER COLUMN "pcc_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ADD CONSTRAINT "UQ_28af845b31c6731110a210e5fa0" UNIQUE ("partner_customer_id")`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" SET DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" DROP CONSTRAINT "UQ_28af845b31c6731110a210e5fa0"`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ALTER COLUMN "pcc_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" ALTER COLUMN "mobile" DROP NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_28af845b31c6731110a210e5fa" ON "los"."user_to_partner_customer" ("partner_customer_id") `);
    }

}
