import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1752227937938 implements MigrationInterface {
  name = 'Migrations1752227937938';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Rename credit_score to credit_analysis
    await queryRunner.query(
      `ALTER TABLE "los"."applicant_details" RENAME COLUMN "credit_score" TO "credit_analysis"`
    );

    // Change credit_analysis type to JSONB
    await queryRunner.query(
      `ALTER TABLE "los"."applicant_details" ALTER COLUMN "credit_analysis" TYPE jsonb 
       USING credit_analysis::jsonb`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert credit_analysis to integer
    await queryRunner.query(
      `ALTER TABLE "los"."applicant_details" 
       ALTER COLUMN "credit_analysis" TYPE integer 
       USING (credit_analysis->>'creditScore')::integer`
    );

    // Revert column name
    await queryRunner.query(
      `ALTER TABLE "los"."applicant_details" 
       RENAME COLUMN "credit_analysis" TO "credit_score"`
    );
  }
}
