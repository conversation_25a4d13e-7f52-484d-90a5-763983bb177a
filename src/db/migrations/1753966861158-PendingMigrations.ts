import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigrations1753966861158 implements MigrationInterface {
    name = 'PendingMigrations1753966861158'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD "airpay_score" numeric(4,2) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" ADD "snapshot_id" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "ruleset_id" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "ruleset_id"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP COLUMN "snapshot_id"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_offers" DROP COLUMN "airpay_score"`);
    }

}
