import { MigrationInterface, QueryRunner } from "typeorm";

export class MadeifscNullableinbankaccount1753541707487 implements MigrationInterface {
    name = 'MadeifscNullableinbankaccount1753541707487'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" ALTER COLUMN "ifsc" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" ALTER COLUMN "ifsc" SET NOT NULL`);
    }

}
