import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailtoApplicantDetail1753644783737 implements MigrationInterface {
    name = 'AddEmailtoApplicantDetail1753644783737'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" ADD "email" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" DROP COLUMN "email"`);
    }

}
