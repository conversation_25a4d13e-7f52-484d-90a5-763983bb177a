import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReasonInFrozentable1756287020630 implements MigrationInterface {
  name = 'AddReasonInFrozentable1756287020630';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "los"."frozen_accounts" ADD "reason" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "los"."frozen_accounts" DROP COLUMN "reason"`);
  }
}
