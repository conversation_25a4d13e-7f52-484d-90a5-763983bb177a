import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedLinkedEntityColumnInAddressTable1753639575986 implements MigrationInterface {
    name = 'AddedLinkedEntityColumnInAddressTable1753639575986'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."addresses" ADD "linked_entity_type" character varying`);
        await queryRunner.query(`ALTER TABLE "los"."addresses" ADD "linked_entity_id" integer`);
        await queryRunner.query(`ALTER TYPE "los"."addresses_type_enum" RENAME TO "addresses_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "los"."addresses_type_enum" AS ENUM('RESIDENTIAL', 'OFFICE', 'CURRENT', 'PERMANENT')`);
        await queryRunner.query(`ALTER TABLE "los"."addresses" ALTER COLUMN "type" TYPE "los"."addresses_type_enum" USING "type"::"text"::"los"."addresses_type_enum"`);
        await queryRunner.query(`DROP TYPE "los"."addresses_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "los"."addresses_type_enum_old" AS ENUM('RESIDENTIAL', 'HOME', 'OFFICE', 'CURRENT', 'PERMANENT')`);
        await queryRunner.query(`ALTER TABLE "los"."addresses" ALTER COLUMN "type" TYPE "los"."addresses_type_enum_old" USING "type"::"text"::"los"."addresses_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "los"."addresses_type_enum"`);
        await queryRunner.query(`ALTER TYPE "los"."addresses_type_enum_old" RENAME TO "addresses_type_enum"`);
        await queryRunner.query(`ALTER TABLE "los"."addresses" DROP COLUMN "linked_entity_id"`);
        await queryRunner.query(`ALTER TABLE "los"."addresses" DROP COLUMN "linked_entity_type"`);
    }

}
