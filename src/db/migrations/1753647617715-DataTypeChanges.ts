import { MigrationInterface, QueryRunner } from "typeorm";

export class DataTypeChanges1753647617715 implements MigrationInterface {
    name = 'DataTypeChanges1753647617715'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" ADD "linked_entity_type" character varying`);
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" ADD "linked_entity_id" integer`);
        await queryRunner.query(`ALTER TABLE "los"."income_verification" ALTER COLUMN "application_id" TYPE integer USING "application_id"::integer`);
        await queryRunner.query(`CREATE INDEX "IDX_afd329814c15ee92015bdcff09" ON "los"."income_verification" ("txnId") `);
        await queryRunner.query(`ALTER TABLE "los"."income_verification" ADD CONSTRAINT "FK_a164aee5eac46f1bdb60d495d07" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."income_verification" DROP CONSTRAINT "FK_a164aee5eac46f1bdb60d495d07"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_afd329814c15ee92015bdcff09"`);
        await queryRunner.query(`ALTER TABLE "los"."income_verification" ALTER COLUMN "application_id" TYPE varchar USING "application_id"::varchar`);
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" DROP COLUMN "linked_entity_id"`);
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" DROP COLUMN "linked_entity_type"`);
    }

}
