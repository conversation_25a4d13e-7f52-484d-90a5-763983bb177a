import { MigrationInterface, QueryRunner } from 'typeorm';

export class Addingindexes1753096321661 implements MigrationInterface {
  name = 'Addingindexes1753096321661';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_USER_USER_ID" ON "los"."loan_applications" ("user_id") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_USER_STATUS_ID" ON "los"."loan_applications" ("status_id") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_USER_CREATED_AT" ON "los"."loan_applications" ("created_at") `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "los"."IDX_USER_CREATED_AT"`);
    await queryRunner.query(`DROP INDEX "los"."IDX_USER_STATUS_ID"`);
    await queryRunner.query(`DROP INDEX "los"."IDX_USER_USER_ID"`);
  }
}
