import { MigrationInterface, QueryRunner } from "typeorm";

export class ModifiedConsentAction1753788961306 implements MigrationInterface {
    name = 'ModifiedConsentAction1753788961306'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "los"."user_consents_action_enum" RENAME TO "user_consents_action_enum_old"`);
        await queryRunner.query(`CREATE TYPE "los"."user_consents_action_enum" AS ENUM('LOGIN', 'DEVICE_PERMISSION', 'LOAN_TNC', 'CIBIL_CONSENT')`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ALTER COLUMN "action" TYPE "los"."user_consents_action_enum" USING "action"::"text"::"los"."user_consents_action_enum"`);
        await queryRunner.query(`DROP TYPE "los"."user_consents_action_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "los"."user_consents_action_enum_old" AS ENUM('LOGIN', 'DEVICE_PERMISSION', 'DIGILOCKER_CONSENT', 'PERSONAL_DETAILS_CONFIRMATION', 'CIBIL_CONSENT', 'ACCOUNT_AGGREGATOR_CONSENT', 'INTERNET_BANKING_CONSENT', 'LOAN_OFFER_ACCEPTANCE', 'REVISED_LOAN_OFFER_ACCEPTANCE', 'E_NACH_CONSENT', 'UPI_AUTOPAY_CONSENT', 'INSURANCE_CONSENT', 'LOAN_DOCUMENTS_ACCEPTANCE', 'FINAL_OTP_CONSENT')`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ALTER COLUMN "action" TYPE "los"."user_consents_action_enum_old" USING "action"::"text"::"los"."user_consents_action_enum_old"`);
        await queryRunner.query(`DROP TYPE "los"."user_consents_action_enum"`);
        await queryRunner.query(`ALTER TYPE "los"."user_consents_action_enum_old" RENAME TO "user_consents_action_enum"`);
    }

}
