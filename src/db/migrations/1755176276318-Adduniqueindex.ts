import { MigrationInterface, QueryRunner } from 'typeorm';

export class Adduniqueindex1755176276318 implements MigrationInterface {
  name = 'Adduniqueindex1755176276318';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_master" ADD CONSTRAINT "UQ_d29b46d695163de681058705823" UNIQUE ("company_pan")`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_master" DROP CONSTRAINT "UQ_d29b46d695163de681058705823"`
    );
  }
}
