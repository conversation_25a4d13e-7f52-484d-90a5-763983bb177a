import { MigrationInterface, QueryRunner } from 'typeorm';

export class IncomeVerification1752750784415 implements MigrationInterface {
  name = 'IncomeVerification1752750784415';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "los"."income_verification" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" character varying(255) NOT NULL, "txnId" character varying(255) NOT NULL, "is_txn_completed" boolean NOT NULL DEFAULT false, "response" jsonb, CONSTRAINT "PK_8032b05c2d014852e8dc2d1ae9e" PRIMARY KEY ("id"))`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "los"."income_verification"`);
  }
}
