import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEventLogTable1753345591005 implements MigrationInterface {
    name = 'AddEventLogTable1753345591005'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."application_event_log" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "event_type" character varying(255) NOT NULL, CONSTRAINT "PK_a6033db5e0283612538b8b24a0f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_APP_EVENT_APP_ID" ON "los"."application_event_log" ("application_id") `);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" ADD "sub_status" character varying`);
        await queryRunner.query(`ALTER TABLE "los"."application_event_log" ADD CONSTRAINT "FK_a20aebb62b066c679bd8be37112" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."application_event_log" DROP CONSTRAINT "FK_a20aebb62b066c679bd8be37112"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" DROP COLUMN "sub_status"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_APP_EVENT_APP_ID"`);
        await queryRunner.query(`DROP TABLE "los"."application_event_log"`);
    }

}
