import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedLendingPartnerIdToLoanApplication1753687955310 implements MigrationInterface {
    name = 'AddedLendingPartnerIdToLoanApplication1753687955310'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" ADD "lending_partner_id" integer`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" ADD CONSTRAINT "FK_dd8816e837cdaec20f3208c144d" FOREIGN KEY ("lending_partner_id") REFERENCES "los"."lending_partners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" DROP CONSTRAINT "FK_dd8816e837cdaec20f3208c144d"`);
        await queryRunner.query(`ALTER TABLE "los"."loan_applications" DROP COLUMN "lending_partner_id"`);
    }

}
