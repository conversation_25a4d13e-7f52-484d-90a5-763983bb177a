import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedColumnsToCpv1756127409645 implements MigrationInterface {
  name = 'AddedColumnsToCpv1756127409645';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."application_video_kycs" ADD "status" character varying`
    );
    await queryRunner.query(`ALTER TABLE "los"."application_cpv" ADD "status" character varying`);
    await queryRunner.query(
      `ALTER TABLE "los"."application_cpv" ADD "completed_at" TIMESTAMP WITH TIME ZONE`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "los"."application_cpv" DROP COLUMN "completed_at"`);
    await queryRunner.query(`ALTER TABLE "los"."application_cpv" DROP COLUMN "status"`);
    await queryRunner.query(`ALTER TABLE "los"."application_video_kycs" DROP COLUMN "status"`);
  }
}
