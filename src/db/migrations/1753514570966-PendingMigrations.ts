import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigrations1753514570966 implements MigrationInterface {
    name = 'PendingMigrations1753514570966'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."application_documents" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "doc_type" character varying(255) NOT NULL, "url" character varying(500) NOT NULL, "doc_name" character varying(255), "confirmation_datetime" TIMESTAMP, "signing_datetime" TIMESTAMP, "sign_type" character varying(100), CONSTRAINT "PK_592142aa992e003beadf1409e9e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_APP_DOC_APP_ID" ON "los"."application_documents" ("application_id") `);
        await queryRunner.query(`ALTER TABLE "los"."application_documents" ADD CONSTRAINT "FK_9ad8ab815e842d67e9aaec900cb" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."application_documents" DROP CONSTRAINT "FK_9ad8ab815e842d67e9aaec900cb"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_APP_DOC_APP_ID"`);
        await queryRunner.query(`DROP TABLE "los"."application_documents"`);
    }
}
