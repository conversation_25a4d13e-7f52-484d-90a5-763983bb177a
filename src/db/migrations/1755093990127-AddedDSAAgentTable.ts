import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedDSAAgentTable1755093990127 implements MigrationInterface {
  name = 'AddedDSAAgentTable1755093990127';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "los"."dsa_agents" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "dsa_id" integer NOT NULL, "name" character varying(100) NOT NULL, "mobile" character varying(12) NOT NULL, "email" character varying(100) NOT NULL, "is_active" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_c91820655e825b1d18503449499" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(`ALTER TABLE "los"."dsa_master" ADD "user_id" integer`);
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_agents" ADD CONSTRAINT "FK_a4d14293e557f0f363a9d0b1268" FOREIGN KEY ("dsa_id") REFERENCES "los"."dsa_master"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_agents" DROP CONSTRAINT "FK_a4d14293e557f0f363a9d0b1268"`
    );
    await queryRunner.query(`ALTER TABLE "los"."dsa_master" DROP COLUMN "user_id"`);
    await queryRunner.query(`DROP TABLE "los"."dsa_agents"`);
  }
}
