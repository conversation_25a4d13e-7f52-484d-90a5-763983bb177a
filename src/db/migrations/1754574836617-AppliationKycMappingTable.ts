import { MigrationInterface, QueryRunner } from 'typeorm';

export class AppliationKycMappingTable1754574836617 implements MigrationInterface {
  name = 'AppliationKycMappingTable1754574836617';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "los"."application_mandates" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "txn_id" character varying(50) NOT NULL, "status" character varying, CONSTRAINT "PK_2b90503be324884a2b678cb9a1b" PRIMARY KEY ("id"))`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "los"."application_mandates"`);
  }
}
