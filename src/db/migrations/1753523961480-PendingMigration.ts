import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigration1753523961480 implements MigrationInterface {
    name = 'PendingMigration1753523961480'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" ADD "customer_name" character varying(100) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."bank_accounts" DROP COLUMN "customer_name"`);
    }

}
