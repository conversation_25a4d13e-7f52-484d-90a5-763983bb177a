import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedKycVerifiedInApplicantDetails1754898219006 implements MigrationInterface {
  name = 'AddedKycVerifiedInApplicantDetails1754898219006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."applicant_details" ADD "kyc_verified" boolean NOT NULL DEFAULT false`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "los"."applicant_details" DROP COLUMN "kyc_verified"`);
  }
}
