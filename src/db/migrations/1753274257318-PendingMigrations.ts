import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigrations1753274257318 implements MigrationInterface {
    name = 'PendingMigrations1753274257318'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "los"."lending_partners_engagementtype_enum" AS ENUM('dlg', 'colending')`);
        await queryRunner.query(`CREATE TABLE "los"."lending_partners" ("id" SERIAL NOT NULL, "name" character varying(255) NOT NULL, "engagementType" "los"."lending_partners_engagementtype_enum" NOT NULL DEFAULT 'dlg', "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_452e629c68ed54be85f2666b598" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "los"."partner_product_mapping" ("id" SERIAL NOT NULL, "partner_id" integer NOT NULL, "product_id" integer NOT NULL, "share_percentage" numeric(5,2) NOT NULL DEFAULT '0', CONSTRAINT "PK_14c8b27dc87ee2d83512a48d788" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" ADD "mobile" character varying(12)`);
        await queryRunner.query(`CREATE TYPE "los"."applicant_details_organisation_type_enum" AS ENUM('PVT_LTD_CO', 'PROPRIETORSHIP', 'PARTNERSHIP', 'LIMITED_CO', 'LLP')`);
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" ADD "organisation_type" "los"."applicant_details_organisation_type_enum"`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" SET DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD CONSTRAINT "FK_d89d3cfa6979d1285eb81fde66b" FOREIGN KEY ("partner_id") REFERENCES "los"."lending_partners"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD CONSTRAINT "FK_d77168e0b71886509e8e9174c9e" FOREIGN KEY ("product_id") REFERENCES "los"."products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP CONSTRAINT "FK_d77168e0b71886509e8e9174c9e"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP CONSTRAINT "FK_d89d3cfa6979d1285eb81fde66b"`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "los"."user_to_partner_customer" ALTER COLUMN "partner_customer_id" SET DEFAULT uuid_generate_v4()`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" DROP COLUMN "organisation_type"`);
        await queryRunner.query(`DROP TYPE "los"."applicant_details_organisation_type_enum"`);
        await queryRunner.query(`ALTER TABLE "los"."applicant_details" DROP COLUMN "mobile"`);
        await queryRunner.query(`DROP TABLE "los"."partner_product_mapping"`);
        await queryRunner.query(`DROP TABLE "los"."lending_partners"`);
        await queryRunner.query(`DROP TYPE "los"."lending_partners_engagementtype_enum"`);
    }

}
