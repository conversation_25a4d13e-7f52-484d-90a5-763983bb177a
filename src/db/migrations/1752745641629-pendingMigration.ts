import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigration1752745641629 implements MigrationInterface {
    name = 'PendingMigration1752745641629'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."pincode_master" ("pincode" character varying(6) NOT NULL, "metadata" jsonb, "state" character varying(100) NOT NULL, "city" character varying(100) NOT NULL, CONSTRAINT "PK_90ba2342b5c877c04834376d7c5" PRIMARY KEY ("pincode"))`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "action"`);
        await queryRunner.query(`CREATE TYPE "los"."user_consents_action_enum" AS ENUM('LOGIN', 'DEVICE_PERMISSION', 'DIGILOCKER_CONSENT', 'PERSONAL_DETAILS_CONFIRMATION', 'CIBIL_CONSENT', 'ACCOUNT_AGGREGATOR_CONSENT', 'INTERNET_BANKING_CONSENT', 'LOAN_OFFER_ACCEPTANCE', 'REVISED_LOAN_OFFER_ACCEPTANCE', 'E_NACH_CONSENT', 'UPI_AUTOPAY_CONSENT', 'INSURANCE_CONSENT', 'LOAN_DOCUMENTS_ACCEPTANCE', 'FINAL_OTP_CONSENT')`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "action" "los"."user_consents_action_enum" NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "action"`);
        await queryRunner.query(`DROP TYPE "los"."user_consents_action_enum"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "action" character varying NOT NULL`);
        await queryRunner.query(`DROP TABLE "los"."pincode_master"`);
    }

}
