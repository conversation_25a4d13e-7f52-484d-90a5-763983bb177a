import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedApplicationRemarksTable1753981446691 implements MigrationInterface {
    name = 'AddedApplicationRemarksTable1753981446691'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."application_remarks" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "remarks" text NOT NULL, "action" character varying(255) NOT NULL, "user_role" character varying(255) NOT NULL, CONSTRAINT "PK_448bcd3dd13d0b4d5bc0ed6f0e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "los"."application_remarks" ADD CONSTRAINT "FK_595b0ba0c50be27548ef8bfabbb" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."application_remarks" DROP CONSTRAINT "FK_595b0ba0c50be27548ef8bfabbb"`);
        await queryRunner.query(`DROP TABLE "los"."application_remarks"`);
    }

}
