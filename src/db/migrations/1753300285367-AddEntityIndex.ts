import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEntityIndex1753300285367 implements MigrationInterface {
    name = 'AddEntityIndex1753300285367'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX "IDX_a2e2e6be6f82649f307867227e" ON "los"."bank_accounts" ("entity_type", "entity_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_32ef7b5ba363a8b429c24dcd33" ON "los"."addresses" ("entity_type", "entity_id") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "los"."IDX_32ef7b5ba363a8b429c24dcd33"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_a2e2e6be6f82649f307867227e"`);
    }

}
