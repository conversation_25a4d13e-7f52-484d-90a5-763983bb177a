import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMigrations1752155139569 implements MigrationInterface {
    name = 'PendingMigrations1752155139569'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "los"."cibil_question_log" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "question" jsonb NOT NULL, "challenge_guid" character varying(50) NOT NULL, "pcc_id" uuid, CONSTRAINT "PK_f84381e8222948fba17ef3e0548" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "los"."user_to_partner_customer" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "user_id" integer NOT NULL, "partner_customer_id" uuid NOT NULL DEFAULT uuid_generate_v4(), CONSTRAINT "PK_e843594f949729ee73fcfb57d6e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_7b6c8759371205cd4222edcf9b" ON "los"."user_to_partner_customer" ("user_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_28af845b31c6731110a210e5fa" ON "los"."user_to_partner_customer" ("partner_customer_id") `);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_0ce1edb67ff64dae6b7faaed5e2" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" ADD CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe" FOREIGN KEY ("pcc_id") REFERENCES "los"."user_to_partner_customer"("partner_customer_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_b36d5c6a678c8afa2abfe0831fe"`);
        await queryRunner.query(`ALTER TABLE "los"."cibil_question_log" DROP CONSTRAINT "FK_0ce1edb67ff64dae6b7faaed5e2"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_28af845b31c6731110a210e5fa"`);
        await queryRunner.query(`DROP INDEX "los"."IDX_7b6c8759371205cd4222edcf9b"`);
        await queryRunner.query(`DROP TABLE "los"."user_to_partner_customer"`);
        await queryRunner.query(`DROP TABLE "los"."cibil_question_log"`);
        await queryRunner.query(`DROP TABLE "los"."user_consents"`);
    }

}
