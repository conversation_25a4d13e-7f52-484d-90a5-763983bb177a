import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedColumnsToDsaMaster1755170020951 implements MigrationInterface {
  name = 'AddedColumnsToDsaMaster1755170020951';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_master" ADD "is_active" boolean NOT NULL DEFAULT false`
    );
    await queryRunner.query(
      `ALTER TABLE "los"."dsa_master" ADD "share_percentage" numeric(5,2) NOT NULL DEFAULT '0'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "los"."dsa_master" DROP COLUMN "share_percentage"`);
    await queryRunner.query(`ALTER TABLE "los"."dsa_master" DROP COLUMN "is_active"`);
  }
}
