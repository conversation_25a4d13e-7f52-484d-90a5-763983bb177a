import { MigrationInterface, QueryRunner } from 'typeorm';

export class CpvAndVideoKyc1755929841109 implements MigrationInterface {
  name = 'CpvAndVideoKyc1755929841109';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "los"."application_video_kycs" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "type" character varying NOT NULL, "is_verified" boolean, "report_link" character varying, CONSTRAINT "PK_c81c154a8d8b9700821ff7756e4" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "los"."application_cpv" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_by" integer, "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "application_id" integer NOT NULL, "type" character varying NOT NULL, "is_verified" boolean, "report_link" character varying, CONSTRAINT "PK_622c8f16d251db967d5e4318c0b" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `ALTER TABLE "los"."application_video_kycs" ADD CONSTRAINT "FK_dc863872b2016cd416e580dd2ec" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "los"."application_cpv" ADD CONSTRAINT "FK_537e695168ff51627b7f989cbc4" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "los"."application_cpv" DROP CONSTRAINT "FK_537e695168ff51627b7f989cbc4"`
    );
    await queryRunner.query(
      `ALTER TABLE "los"."application_video_kycs" DROP CONSTRAINT "FK_dc863872b2016cd416e580dd2ec"`
    );
    await queryRunner.query(`DROP TABLE "los"."application_cpv"`);
    await queryRunner.query(`DROP TABLE "los"."application_video_kycs"`);
  }
}
