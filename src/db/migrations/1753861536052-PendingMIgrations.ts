import { MigrationInterface, QueryRunner } from "typeorm";

export class PendingMIgrations1753861536052 implements MigrationInterface {
    name = 'PendingMIgrations1753861536052'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD "application_id" integer`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" ADD CONSTRAINT "FK_9d98d34165f359338ccc0b58b5f" FOREIGN KEY ("application_id") REFERENCES "los"."loan_applications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP CONSTRAINT "FK_9d98d34165f359338ccc0b58b5f"`);
        await queryRunner.query(`ALTER TABLE "los"."user_consents" DROP COLUMN "application_id"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "los"."partner_product_mapping" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "los"."lending_partners" DROP COLUMN "created_at"`);
    }

}
