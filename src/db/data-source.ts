import { DataSourceOptions, DataSource } from 'typeorm';
import { config as dotenvConfig } from 'dotenv';
import * as path from 'path';
dotenvConfig({ path: '.env' });
const isCompiled = __filename.endsWith('.js');

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT!) || 5432,
  username: process.env.DB_USERNAME ?? 'postgres',
  password: process.env.DB_PASSWORD ?? 'postgres',
  database: process.env.DB_NAME ?? 'test_nest',
  logging: process.env.QUERY_LOG === 'true',
  entities: isCompiled
    ? [path.join(__dirname, '/../**/*.entity.js')]
    : [path.join(__dirname, '/../**/*.entity.ts')],
  migrations: [path.join(__dirname, '/migrations/*.js')],
  migrationsRun: true,
  schema: process.env.DB_SCHEMA ?? 'public',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  cache: {
    type: 'ioredis',
    options: {
      host: process.env.REDIS_HOST ?? '127.0.0.1',
      port: 6379,
      password: process.env.REDIS_PASSWORD ?? '',
    },
  },
};

const dataSource = new DataSource(dataSourceOptions);

export default dataSource;
