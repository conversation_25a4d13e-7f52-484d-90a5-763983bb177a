import { Controller, Post, Body, Get, Query, UseGuards } from '@nestjs/common';
import { DsaManagementService } from './dsa-management.service';
import { CreateDsaManagementDto } from './dto/dsa-management.dto';
import { fixed_response } from '../utils/response-util';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { Roles } from 'src/common/decorators/role.decorator';

@Controller('dsa-agency')
@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('admin')
export class DsaManagementController {
  constructor(private readonly dsaService: DsaManagementService) {}

  @Post()
  async create(@Body() dto: CreateDsaManagementDto) {
    return fixed_response(await this.dsaService.create(dto));
  }

  @Get()
  async findAll(@Query() query: any) {
    return fixed_response(await this.dsaService.findAll(query));
  }
}
