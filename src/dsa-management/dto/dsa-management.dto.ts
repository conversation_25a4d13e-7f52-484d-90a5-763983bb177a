import {
  IsNotEmpty,
  IsString,
  IsEmail,
  Length,
  IsBoolean,
  IsOptional,
  IsNumber,
} from 'class-validator';

export class CreateDsaManagementDto {
  @IsNotEmpty()
  @IsString()
  agencyName: string;

  @IsNotEmpty()
  @IsString()
  @Length(10, 15)
  contactNumber: string;

  @IsNotEmpty()
  @IsString()
  contactPersonName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  companyPan: string;

  @IsNotEmpty()
  @IsString()
  operationRegion: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsNotEmpty()
  @IsNumber()
  sharePercentage: number;
}
