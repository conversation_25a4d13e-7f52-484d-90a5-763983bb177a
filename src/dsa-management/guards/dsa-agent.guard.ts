import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DsaAgent } from '../entity/dsa-agents.entity';

@Injectable()
export class DsaAgentGuard implements CanActivate {
  constructor(
    private readonly cls: ClsService,
    @InjectRepository(DsaAgent)
    private readonly dsaAgentRepo: Repository<DsaAgent>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const userId = this.cls.get<number>('userId');

    if (!userId) {
      throw new UnauthorizedException('User ID not found in CLS');
    }

    const dsaAgent = await this.dsaAgentRepo
      .createQueryBuilder('dsaAgent')
      .innerJoinAndSelect('dsaAgent.dsa', 'dsa')
      .where('dsaAgent.userId = :userId', { userId })
      .andWhere('dsaAgent.isActive = true')
      .andWhere('dsa.isActive = true')
      .getOne();

    if (!dsaAgent) {
      throw new UnauthorizedException(`No dsaAgent record found for userId ${userId}`);
    }

    this.cls.set('dsaAgentId', dsaAgent.id);

    return true;
  }
}
