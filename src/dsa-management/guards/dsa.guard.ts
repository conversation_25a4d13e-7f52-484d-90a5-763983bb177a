import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Dsa } from '../entity/dsa-management.entity';

@Injectable()
export class DsaGuard implements CanActivate {
  constructor(
    private readonly cls: ClsService,
    @InjectRepository(Dsa)
    private readonly dsaRepo: Repository<Dsa>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const userId = this.cls.get<number>('userId');

    if (!userId) {
      throw new UnauthorizedException('User ID not found in CLS');
    }

    const dsa = await this.dsaRepo.findOne({ where: { userId, isActive: true } });

    if (!dsa) {
      throw new UnauthorizedException(`No DSA record found for userId ${userId}`);
    }

    this.cls.set('dsa', dsa.id);

    return true;
  }
}
