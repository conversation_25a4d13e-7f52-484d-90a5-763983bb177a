import { <PERSON>, Post, Body, Get, Query, UseGuards, Param, Put } from '@nestjs/common';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { DsaAgentService } from './dsa-agent.service';
import { CreateDsaAgentDto } from './dto/dsa-agents.dto';
import { fixed_response } from 'src/utils/response-util';
import { Roles } from 'src/common/decorators/role.decorator';
import { DsaGuard } from './guards/dsa.guard';
import { UpdateActiveStatusDto } from './dto/update-active-status.dto';

@Controller('dsa-agents')
@UseGuards(DecodeTokenGuard, RoleGuard, DsaGuard)
@Roles('dsa-admin')
export class DsaAgentController {
  constructor(private readonly agentService: DsaAgentService) {}

  @Post()
  async create(@Body() dto: CreateDsaAgentDto) {
    return fixed_response(await this.agentService.create(dto));
  }

  @Get()
  async findAll(@Query() query: any) {
    return fixed_response(await this.agentService.findAll(query));
  }

  @Put(':id/active-status')
  async updateActiveStatus(@Param('id') id: number, @Body() body: UpdateActiveStatusDto) {
    return fixed_response(await this.agentService.updateActiveStatus(id, body.isActive));
  }
}
