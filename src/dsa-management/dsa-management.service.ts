import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Message, parseQuery } from 'src/utils/response-util';
import { CreateDsaManagementDto } from './dto/dsa-management.dto';
import { Dsa } from './entity/dsa-management.entity';

@Injectable()
export class DsaManagementService {
  constructor(
    @InjectRepository(Dsa)
    private readonly dsaRepo: Repository<Dsa>,
    private readonly dataSource: DataSource
  ) {}

  async create(dto: CreateDsaManagementDto): Promise<Message> {
    try {
      const saved = await this.dataSource.transaction(async (manager) => {
        await manager.query('LOCK TABLE los.dsa_master IN EXCLUSIVE MODE');

        const existing = await manager.findOne(Dsa, {
          where: { companyPan: dto.companyPan },
        });

        if (existing) {
          return { success: false, error: 'DSA with this company PAN already exists' };
        }

        const raw = await manager
          .createQueryBuilder(Dsa, 'd')
          .select('d.dsa_code', 'dsa_code')
          .where('d.dsa_code IS NOT NULL')
          .orderBy('d.id', 'DESC')
          .limit(1)
          .getRawOne();

        let nextNum = 1;
        if (raw && raw.dsa_code) {
          const match = raw.dsa_code.match(/^DSA0*([0-9]+)$/i);
          if (match) {
            nextNum = parseInt(match[1], 10) + 1;
          }
        }

        const dsaCode = this.formatCode(nextNum);

        // Create and save new record
        const dsa = manager.create(Dsa, {
          ...dto,
          dsaCode,
        });

        return await manager.save(dsa);
      });

      return { success: true, data: saved };
    } catch (err) {
      console.error(err);
      return { success: false, error: 'Failed to create DSA record' };
    }
  }

  async findAll(query: any): Promise<Message> {
    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'createdAt',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }

    const whereClause = rawQuery ? parseQuery(rawQuery) : {};

    try {
      const [data, total] = await this.dsaRepo.findAndCount({
        where: whereClause,
        order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
        skip,
        take: limitNum,
      });

      return {
        success: true,
        message: 'DSA records fetched successfully',
        data,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
      };
    } catch (error) {
      return { success: false, error: 'Failed to fetch DSA records' };
    }
  }
  private formatCode(num: number) {
    return `DSA${String(num).padStart(3, '0')}`;
  }
}
