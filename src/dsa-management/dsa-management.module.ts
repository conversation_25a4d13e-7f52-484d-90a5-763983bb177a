import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DsaManagementService } from './dsa-management.service';
import { DsaManagementController } from './dsa-management.controller';
import { Dsa } from './entity/dsa-management.entity';
import { DsaAgentController } from './dsa-agent.controller';
import { DsaAgentService } from './dsa-agent.service';
import { DsaAgent } from './entity/dsa-agents.entity';
import { HttpModule } from '@nestjs/axios';
import { DsaAgentLoanController } from './dsa-agent-loan.controller';
import { DsaAgentLoanService } from './dsa-agent-loan.service';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([Dsa, DsaAgent, LoanApplication, Address, BankAccount]),
  ],
  controllers: [DsaManagementController, DsaAgentController, DsaAgentLoanController],
  providers: [DsaManagementService, DsaAgentService, DsaAgentLoanService],
})
export class DsaManagementModule {}
