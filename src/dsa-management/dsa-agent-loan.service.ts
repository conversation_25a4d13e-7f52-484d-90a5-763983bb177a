import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Message, parseQuery } from 'src/utils/response-util';
import { Address } from 'src/addresses/entity/address.entity';

@Injectable()
export class DsaAgentLoanService {
  private readonly logger = new Logger(DsaAgentLoanService.name);

  constructor(
    @InjectRepository(LoanApplication)
    private readonly loanAppRepo: Repository<LoanApplication>,
    @InjectRepository(Address)
    private readonly addressRepo: Repository<Address>,
    private readonly cls: ClsService
  ) {}

  async getAll(query: any): Promise<Message> {
    this.logger.log('Hitting getAll service method');

    const dsaAgentId = this.cls.get<number>('dsaAgentId');
    this.logger.debug('dsaAgentId>>', dsaAgentId);

    if (!dsaAgentId) {
      throw new Error('Dsa Agent ID is required');
    }

    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'createdAt',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;
    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }
    const whereClause = rawQuery ? parseQuery(rawQuery) : {};
    whereClause['dsaAgentId'] = dsaAgentId;

    const [data, total] = await this.loanAppRepo.findAndCount({
      where: whereClause,
      relations: ['applicantDetail', 'product', 'status', 'lendingPartner'],
      order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
      skip,
      take: limitNum,
    });

    return {
      success: true,
      message: 'Loan applications fetched successfully',
      data,
      total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(total / limitNum),
    };
  }

  async getById(loanId: number): Promise<Message> {
    this.logger.log('Hitting getById service method');

    const dsaAgentId = this.cls.get<number>('dsaAgentId');

    if (!dsaAgentId) {
      throw new Error('Dsa Agent ID is required');
    }

    const record = await this.loanAppRepo.findOne({
      where: { id: loanId, dsaAgentId },
      relations: [
        'product',
        'loanPurpose',
        'status',
        'applicantDetail',
        'offers',
        'lendingPartner',
        'documents',
      ],
    });

    this.logger.debug('Hitting getById >>', record?.applicantDetail.id);

    if (!record) {
      return {
        success: false,
        message: 'Loan application not found',
        error: 'Loan application not found',
      };
    }

    record.addresses = await this.addressRepo.find({
      where: {
        linkedEntityType: 'applicant_details',
        linkedEntityId: record.applicantDetail.id,
      },
    });

    return {
      success: true,
      message: 'Loan application fetched successfully',
      data: record,
    };
  }
}
