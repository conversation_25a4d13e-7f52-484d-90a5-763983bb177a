import { Controller, UseGuards, Get, Query, Logger, ParseIntPipe, Param } from '@nestjs/common';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { DsaAgentLoanService } from './dsa-agent-loan.service';
import { fixed_response } from 'src/utils/response-util';
import { DsaAgentGuard } from './guards/dsa-agent.guard';

@UseGuards(DecodeTokenGuard, DsaAgentGuard)
@Controller('dsa/loan-application')
export class DsaAgentLoanController {
  private readonly logger = new Logger(DsaAgentLoanController.name);
  constructor(private readonly dsaAgentLoanService: DsaAgentLoanService) {}

  @Get()
  async getAll(@Query() query: any) {
    this.logger.log('Hitting getAll API');
    return fixed_response(await this.dsaAgentLoanService.getAll(query));
  }

  @Get(':id')
  async getOne(@Param('id', ParseIntPipe) id: number) {
    this.logger.log('Hitting getOne API');

    return fixed_response(await this.dsaAgentLoanService.getById(id));
  }
}
