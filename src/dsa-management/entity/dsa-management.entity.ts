import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity({ name: 'dsa_master' })
export class Dsa extends CustomBaseEntity {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'agency_name', type: 'varchar', length: 255 })
  agencyName: string;

  @Column({ name: 'contact_number', type: 'varchar', length: 12 })
  contactNumber: string;

  @Column({ name: 'contact_person_name', type: 'varchar', length: 255 })
  contactPersonName: string;

  @Column({ name: 'email', type: 'varchar', length: 255 })
  email: string;

  @Column({ name: 'company_pan', type: 'varchar', length: 10, unique: true })
  companyPan: string;

  @Column({ name: 'operation_region', type: 'varchar', length: 255 })
  operationRegion: string;

  @Column({ name: 'dsa_code', type: 'varchar', length: 6, unique: true })
  dsaCode: string;

  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId: number;

  @Column({ name: 'is_active', type: 'boolean', default: false })
  isActive: boolean;

  @Column({ name: 'share_percentage', type: 'decimal', precision: 5, scale: 2, default: 0 })
  sharePercentage: number;
}
