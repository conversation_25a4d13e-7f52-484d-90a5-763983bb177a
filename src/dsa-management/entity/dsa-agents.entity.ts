import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>umn } from 'typeorm';
import { Dsa } from './dsa-management.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity({ name: 'dsa_agents' })
export class DsaAgent extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Dsa, { eager: false })
  @JoinColumn({ name: 'dsa_id' })
  dsa: Dsa;

  @Column({ name: 'dsa_id', type: 'int' })
  dsaId: number;

  @Column({ name: 'name', type: 'varchar', length: 100 })
  name: string;

  @Column({ name: 'mobile', type: 'varchar', length: 12 })
  mobile: string;

  @Column({ name: 'email', type: 'varchar', length: 100 })
  email: string;

  @Column({ name: 'is_active', type: 'boolean', default: false })
  isActive: boolean;

  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId: number;
}
