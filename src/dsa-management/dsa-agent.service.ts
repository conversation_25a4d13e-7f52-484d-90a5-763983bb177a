import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DsaAgent } from './entity/dsa-agents.entity';
import { CreateDsaAgentDto } from './dto/dsa-agents.dto';
import { parseQuery } from 'src/utils/response-util';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class DsaAgentService {
  private readonly logger = new Logger(DsaAgentService.name);
  private readonly authServiceBaseUrl: string;

  constructor(
    @InjectRepository(DsaAgent)
    private readonly agentRepo: Repository<DsaAgent>,
    private readonly cls: ClsService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {
    this.authServiceBaseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
  }

  async create(dto: CreateDsaAgentDto): Promise<Message> {
    const dsaId = this.cls.get<number>('dsa');
    const { notify, ...agentData } = dto;

    const url = `${this.authServiceBaseUrl}/auth/api/admin/auth/search-mobile`;

    const header = this.cls.get('header');

    this.logger.log('Calling auth-service search-mobile API ');

    const res = await firstValueFrom(
      this.httpService.post(
        url,
        { mobileNumber: dto.mobile },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${header?.authorization}`,
          },
        }
      )
    );

    this.logger.log('Auth search-mobile response: >', res.data, res.status);
    let userIdfromAuth: any = res?.data?.data?.id;

    if (!res?.data?.data) {
      const url2 = `${this.authServiceBaseUrl}/auth/api/admin/auth/create-user`;

      const header = this.cls.get('header');

      this.logger.log('Calling create-user API ');

      const res = await firstValueFrom(
        this.httpService.post(
          url2,
          {
            firstName: dto.name,
            mobile: dto.mobile,
            email: dto.email,
            is2faEnabled: false,
            roles: ['a013eb0f-0484-4fa9-9989-dcbd164aff11'],
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `${header?.authorization}`,
            },
          }
        )
      );

      this.logger.log('Auth create-user response: >', res.data, res.status);
      userIdfromAuth = res?.data?.data?.id;
    }

    const existingAgent = await this.agentRepo.findOne({
      where: { userId: Number(userIdfromAuth) },
    });

    this.logger.debug('Existing agent check:', existingAgent);
    if (existingAgent) {
      throw new Error('Agent with this userId already exists');
    }

    const agent = this.agentRepo.create({
      ...agentData,
      userId: Number(userIdfromAuth),
      dsaId: dsaId,
    });
    const saved = await this.agentRepo.save(agent);

    return { success: true, data: saved.id, message: 'dsa agent created successfully' };
  }

  async findAll(query: any): Promise<Message> {
    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'createdAt',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }

    const whereClause = rawQuery ? parseQuery(rawQuery) : {};
    const dsaId = this.cls.get<number>('dsa');

    const finalObj = {
      ...whereClause,
      dsaId: dsaId,
    };

    try {
      const [data, total] = await this.agentRepo.findAndCount({
        where: finalObj,
        order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
        skip,
        take: limitNum,
      });

      return {
        success: true,
        message: 'Agents fetched successfully',
        data,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
      };
    } catch (error) {
      console.error(error);
      return { success: false, error: 'Failed to fetch agents' };
    }
  }

  async updateActiveStatus(agentId: number, isActive: boolean): Promise<Message> {
    const dsaId = this.cls.get<number>('dsa');

    try {
      const agent = await this.agentRepo.findOne({ where: { id: agentId, dsaId } });

      if (!agent) {
        return {
          success: false,
          error: 'Agent not found',
        };
      }

      agent.isActive = isActive;

      await this.agentRepo.save(agent);

      return {
        success: true,
        data: { id: agent.id, isActive: agent.isActive },
        message: `Agent active status updated successfully to ${agent.isActive}`,
      };
    } catch (err) {
      console.error('Error in updateActiveStatus:', err);
      return {
        success: false,
        error: 'Failed to update agent active status',
      };
    }
  }
}
