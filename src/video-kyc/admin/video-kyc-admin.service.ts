import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClsService } from 'nestjs-cls';
import { ApplicationVideoKyc } from '../entity/application-video-kyc.entity';
import { Message, parseQuery } from 'src/utils/response-util';

@Injectable()
export class VideoKycAdminService {
  constructor(
    @InjectRepository(ApplicationVideoKyc)
    private readonly videoKycRepo: Repository<ApplicationVideoKyc>,
    private readonly cls: ClsService
  ) {}

  async getAll(query: any): Promise<Message> {
    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'id',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }

    const whereClause = rawQuery ? parseQuery(rawQuery) : {};
    try {
      const [data, total] = await this.videoKycRepo.findAndCount({
        relations: ['application'],
        where: whereClause,
        order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
        skip,
        take: limitNum,
      });

      return {
        success: true,
        message: 'Video KYC records fetched successfully',
        data,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
      };
    } catch (error) {
      console.error(error);
      return { success: false, error: 'Failed to fetch Video KYC records' };
    }
  }
}
