import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { Roles } from 'src/common/decorators/role.decorator';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { VideoKycAdminService } from './video-kyc-admin.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from '../../common/guards/decode-token.guard';

@Controller('admin/video-kyc')
@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('admin')
@Controller('admin/video-kyc')
export class VideoKycAdminController {
  constructor(private readonly videoKycService: VideoKycAdminService) {}

  @Get()
  async getAll(@Query() query: any) {
    return fixed_response(await this.videoKycService.getAll(query));
  }
}
