// src/video-kyc/video-kyc.module.ts

import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { VideoKycService } from './video-kyc.service';
import { VideoKycController } from './video-kyc.controller';
import { VideoKycAdminService } from './admin/video-kyc-admin.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationVideoKyc } from './entity/application-video-kyc.entity';
import { VideoKycAdminController } from './admin/video-kyc-admin.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationVideoKyc]), HttpModule],
  providers: [VideoKycService, VideoKycAdminService],
  controllers: [VideoKycController, VideoKycAdminController],
})
export class VideoKycModule {}
