// src/video-kyc/video-kyc.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class VideoKycService {
  private readonly logger = new Logger(VideoKycService.name);
  private readonly baseUrl: any;

  constructor(
    private readonly httpService: HttpService,
    private readonly cls: ClsService,
    private readonly configService: ConfigService
  ) {
    this.baseUrl = this.configService.getOrThrow<string>('KYC_SERVICE_BASE_URL');
  }

  async performVideoKyc(): Promise<Message> {
    try {
      const payload = {
        firstName: 'abjndjc',
        lastName: 'jdsdb',
        currentAddress: 'mumbai',
        permanentAddress: 'mumbai',
        applicationType: 'INDIVIDUAL',
      };

      const initiateRes = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/api/video-kyc/customer-onboarding`, payload, {
          headers: {
            Authorization: this.cls.get('header')['authorization'],
          },
        })
      );

      if (!initiateRes.data.success) {
        return { success: false, error: 'Failed to initiate video KYC' };
      }

      const weblinkRes = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/api/video-kyc/customer-web-link`,
          {
            userToken: initiateRes.data.data.userToken,
          },
          {
            headers: {
              Authorization: this.cls.get('header')['authorization'],
            },
          }
        )
      );

      if (!weblinkRes.data.success) {
        return { success: false, error: 'Failed to get video KYC web link' };
      }

      return {
        success: true,
        data: { ...weblinkRes.data.data },
      };
    } catch (err: any) {
      this.logger.error('Video KYC failed', err.message);
      return {
        success: false,
        error: err.message || 'Failed to perform video KYC',
      };
    }
  }
}
