import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { LoanApplication } from '../../loan-application/entity/loan-application.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity('application_video_kycs')
export class ApplicationVideoKyc extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @ManyToOne(() => LoanApplication)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Column({ name: 'type', type: 'varchar' })
  type: string;

  @Column({ name: 'is_verified', type: 'boolean', nullable: true })
  isVerified: boolean | null;

  @Column({ name: 'report_link', type: 'varchar', nullable: true })
  reportLink: string;

  @Column({ name: 'status', type: 'varchar', nullable: true })
  status: string | null;
}
