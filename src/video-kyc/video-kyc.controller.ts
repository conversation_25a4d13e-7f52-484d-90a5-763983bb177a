// src/video-kyc/video-kyc.controller.ts
import { Controller, Post, UseGuards } from '@nestjs/common';
import { VideoKycService } from './video-kyc.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('video-kyc')
export class VideoKycController {
  constructor(private readonly svc: VideoKycService) {}

  @Post('initiate')
  async create() {
    return fixed_response(await this.svc.performVideoKyc());
  }
}
