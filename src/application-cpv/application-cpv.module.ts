// src/modules/application-cpv/application-cpv.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationCpv } from './entity/application-cpv.entity';
import { ApplicationCpvService } from './application-cpv.service';
import { ApplicationCpvController } from './application-cpv.controller';
import { FileUploadModule } from 'src/file-upload/file-upload.module';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationCpv]), FileUploadModule],
  controllers: [ApplicationCpvController],
  providers: [ApplicationCpvService],
})
export class ApplicationCpvModule {}
