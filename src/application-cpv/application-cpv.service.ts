import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Repository } from 'typeorm';
import { ApplicationCpv } from './entity/application-cpv.entity';
import { ClsService } from 'nestjs-cls';
import { Message, parseQuery } from 'src/utils/response-util';
import { FileUploadService } from 'src/file-upload/file-upload.service';

@Injectable()
export class ApplicationCpvService {
  constructor(
    @InjectRepository(ApplicationCpv)
    private readonly cpvRepo: Repository<ApplicationCpv>,
    private readonly cls: ClsService,
    private readonly fileUploadService: FileUploadService
  ) {}

  async getAll(query: any): Promise<Message> {
    const {
      page = '1',
      limit = '10',
      order = 'DESC',
      orderBy = 'id',
      query: encodedQuery = '',
    } = query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    let rawQuery = '';
    try {
      rawQuery = decodeURIComponent(String(encodedQuery));
    } catch {
      rawQuery = '';
    }

    const whereClause = rawQuery ? parseQuery(rawQuery) : {};

    try {
      const [data, total] = await this.cpvRepo.findAndCount({
        relations: ['application'],
        where: whereClause,
        order: { [orderBy]: order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC' },
        skip,
        take: limitNum,
      });

      return {
        success: true,
        message: 'CPV records fetched successfully',
        data,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
      };
    } catch (error) {
      console.error(error);
      return {
        success: false,
        error: 'Failed to fetch CPV records',
      };
    }
  }

  async uploadReport(id: number, file: Express.Multer.File): Promise<Message> {
    if (!file) throw new BadRequestException('File is required');

    const cpv = await this.cpvRepo.findOne({ where: { id } });
    if (!cpv) return { success: false, error: 'cpv not found' };

    const upload = await this.fileUploadService.uploadFile(file);
    if (!upload.success) {
      return { success: false, error: 'Failed to upload CPV report' };
    }
    const key = upload.data.key;

    cpv.reportLink = key as unknown as string;
    cpv.status = 'UPLOADED';
    await this.cpvRepo.save(cpv);

    return { success: true, message: 'CPV report uploaded', data: cpv.id };
  }

  async generateReportLink(id: number): Promise<Message> {
    const cpv = await this.cpvRepo.findOne({ where: { id } });
    if (!cpv || !cpv.reportLink) {
      return { success: false, error: 'cpv not found' };
    }

    const bucket = this.fileUploadService['config'].s3Bucket;
    const signedUrl = await this.fileUploadService.generateSignedUrl(
      bucket,
      String(cpv.reportLink)
    );
    return { success: true, data: { signedUrl } };
  }

  async setApprovalStatus(id: number, action: 'APPROVE' | 'REJECT'): Promise<Message> {
    const cpv = await this.cpvRepo.findOne({ where: { id, status: IsNull() } });
    if (!cpv) return { success: false, error: 'cpv not found' };

    cpv.status = action === 'APPROVE' ? 'APPROVED' : 'REJECTED';
    cpv.completedAt = new Date();
    await this.cpvRepo.save(cpv);

    return { success: true, message: `CPV ${action.toLowerCase()}d` };
  }
}
