import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity('application_cpv')
export class ApplicationCpv extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @ManyToOne(() => LoanApplication)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Column({ name: 'type', type: 'varchar' })
  type: string;

  @Column({ name: 'is_verified', type: 'boolean', nullable: true })
  isVerified: boolean | null;

  @Column({ name: 'report_link', type: 'varchar', nullable: true })
  reportLink: string;

  @Column({ name: 'status', type: 'varchar', nullable: true })
  status: string | null;

  @Column({ name: 'completed_at', type: 'timestamptz', nullable: true })
  completedAt: Date | null;
}
