import {
  Controller,
  Get,
  Post,
  Param,
  ParseIntPipe,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Body,
  ParseFilePipe,
  MaxFileSizeValidator,
} from '@nestjs/common';
import { ApplicationCpvService } from './application-cpv.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/role.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('admin')
@Controller('admin/application-cpv')
export class ApplicationCpvController {
  constructor(private readonly cpvService: ApplicationCpvService) {}

  @Get()
  async getAll(@Query() query: any) {
    return fixed_response(await this.cpvService.getAll(query));
  }

  @Post(':id/upload-report')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: '/tmp',
        filename: (_, file, cb) => cb(null, file.originalname),
      }),
    })
  )
  async uploadReport(
    @Param('id', ParseIntPipe) id: number,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 })],
      })
    )
    file: Express.Multer.File
  ) {
    return fixed_response(await this.cpvService.uploadReport(id, file));
  }

  @Get(':id/report-link')
  async generateReportLink(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.cpvService.generateReportLink(id));
  }

  @Post(':id/decision')
  async approveOrReject(
    @Param('id', ParseIntPipe) id: number,
    @Body('action') action: 'APPROVE' | 'REJECT'
  ) {
    return fixed_response(await this.cpvService.setApprovalStatus(id, action));
  }
}
