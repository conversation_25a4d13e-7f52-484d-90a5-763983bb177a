import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserConsent } from './entity/user-consent.entity';
import { CreateUserConsentDto } from './dto/user-consent.dto';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class UserConsentService {
  constructor(
    @InjectRepository(UserConsent)
    private readonly consentRepo: Repository<UserConsent>,
    private readonly cls: ClsService
  ) {}

  async create(dto: CreateUserConsentDto): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const newConsent = this.consentRepo.create({
        userId,
        consentText: dto.consentText,
        consentAcceptanceTime: new Date(),
        action: dto.action,
      });

      const saved = await this.consentRepo.save(newConsent);

      return {
        success: true,
        data: {
          message: 'Consent recorded successfully',
          id: saved.id,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message ?? 'Failed to record consent',
      };
    }
  }
}
