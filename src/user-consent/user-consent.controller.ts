import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { UserConsentService } from './user-consent.service';
import { CreateUserConsentDto } from './dto/user-consent.dto';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('user-consents')
export class UserConsentController {
  constructor(private readonly consentService: UserConsentService) {}

  @Post()
  async create(@Body() dto: CreateUserConsentDto) {
    return fixed_response(await this.consentService.create(dto));
  }
}
