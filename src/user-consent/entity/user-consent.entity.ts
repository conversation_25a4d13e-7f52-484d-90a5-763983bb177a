import { <PERSON>tity, PrimaryGeneratedColumn, Column, <PERSON>To<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ConsentAction } from '../dto/user-consent.dto';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity({ name: 'user_consents' })
export class UserConsent extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'consent_text', type: 'text', nullable: true })
  consentText?: string;

  @Column({ name: 'consent_acceptance_time', type: 'timestamp' })
  consentAcceptanceTime: Date;

  @Column({
    type: 'enum',
    enum: ConsentAction,
  })
  action: ConsentAction;

  @Column({ name: 'application_id', type: 'int', nullable: true })
  applicationId?: number;

  @ManyToOne(() => LoanApplication, (app) => app.userConsents, {
    nullable: true,
  })
  @JoinColumn({ name: 'application_id' })
  application?: LoanApplication;
}
