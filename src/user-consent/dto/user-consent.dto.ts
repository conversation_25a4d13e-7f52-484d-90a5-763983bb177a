import { IsString, IsOptional, IsNotEmpty, IsE<PERSON> } from 'class-validator';

export enum ConsentAction {
  LOGIN = 'LOGIN',
  DEVICE_PERMISSION = 'DEVICE_PERMISSION',
  LOAN_TNC = 'LOAN_TNC',
  CIBIL_CONSENT = 'CIBIL_CONSENT',
}

export class CreateUserConsentDto {
  @IsOptional()
  @IsString()
  consentText?: string;

  @IsNotEmpty()
  @IsEnum(ConsentAction, {
    message: `action must be one of: ${Object.values(ConsentAction).join(', ')}`,
  })
  action: ConsentAction;

  @IsOptional()
  applicationId?: number;
}
