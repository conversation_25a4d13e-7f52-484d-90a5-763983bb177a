import { Module } from '@nestjs/common';
import { UserConsentService } from './user-consent.service';
import { UserConsentController } from './user-consent.controller';
import { UserConsent } from './entity/user-consent.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([UserConsent])],
  controllers: [UserConsentController],
  providers: [UserConsentService],
})
export class UserConsentModule {}
