import serverlessExpress from '@codegenie/serverless-express';
import { Callback, Context, Handler } from 'aws-lambda';
import { createApp } from './bootstrap-app';
import { Request, Response, NextFunction } from 'express';

let server: Handler;

async function bootstrap(): Promise<Handler> {
  const app = await createApp();

  const allowedOrigins = [
    'http://capital-webview-dev.s3-website.ap-south-1.amazonaws.com',
    'http://localhost:5174',
    'http://capital-lms-dev.s3-website.ap-south-1.amazonaws.com',
    'http://localhost:5173',
  ];

  const corsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    const origin = req.headers.origin;

    if (origin && allowedOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin); // dynamically reflect allowed origin
      res.header('Access-Control-Allow-Credentials', 'true');
    }

    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
  };

  app.use(corsMiddleware);

  await app.init();
  console.log('App started successfully');
  const expressApp = app.getHttpAdapter().getInstance();
  return serverlessExpress({ app: expressApp });
}

export const handler: Handler = async (event: any, context: Context, callback: Callback) => {
  server = server ?? (await bootstrap());
  return server(event, context, callback);
};
