import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LookupMaster } from './entity/lookup-master.entity';
import { LookupMasterService } from './lookup-master.service';
import { LookupMasterController } from './lookup-master.controller';

@Module({
  imports: [TypeOrmModule.forFeature([LookupMaster])],
  providers: [LookupMasterService],
  controllers: [LookupMasterController],
  exports: [LookupMasterService],
})
export class LookupMasterModule {}
