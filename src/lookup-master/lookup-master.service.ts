import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LookupMaster } from './entity/lookup-master.entity';
import { Repository } from 'typeorm';
import { Message } from 'src/utils/response-util';

@Injectable()
export class LookupMasterService {
  constructor(
    @InjectRepository(LookupMaster)
    private readonly lookupRepo: Repository<LookupMaster>
  ) {}

  async getMultipleCategories(categories: string[]): Promise<Message> {
    if (!categories.length) {
      return {
        success: false,
        error: 'At least one category must be specified',
      };
    }

    const lookups = await this.lookupRepo
      .createQueryBuilder('lookup')
      .where('lookup.category IN (:...categories)', { categories })
      .getMany();

    const grouped = categories.reduce(
      (acc, cat) => {
        acc[cat] = lookups.filter((l) => l.category === cat);
        return acc;
      },
      {} as Record<string, LookupMaster[]>
    );

    return {
      success: true,
      data: grouped,
    };
  }
}
