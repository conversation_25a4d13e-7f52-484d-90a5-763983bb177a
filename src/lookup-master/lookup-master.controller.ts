import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { LookupMasterService } from './lookup-master.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('lookup-master')
export class LookupMasterController {
  constructor(private readonly lookupService: LookupMasterService) {}

  @Get()
  async getLookups(@Query('category') category: string) {
    const categories = category?.split(',').map((c) => c.trim()) || [];
    return fixed_response(await this.lookupService.getMultipleCategories(categories));
  }
}
