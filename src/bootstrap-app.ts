import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import { CustomValidationPipe } from './common/pipes/custom-validation-pipe';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

export async function createApp() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  const loggerLevels = (configService.get<string>('LOGGER_LEVEL') ?? 'error,fatal').split(',') as (
    | 'log'
    | 'error'
    | 'warn'
    | 'debug'
    | 'verbose'
    | 'fatal'
  )[];

  app.use(helmet());
  app.useLogger(loggerLevels);
  app.enableCors();

  app.useGlobalPipes(new CustomValidationPipe());

  app.setGlobalPrefix('api');
  app.useGlobalFilters(new GlobalExceptionFilter());

  app.useGlobalInterceptors(new LoggingInterceptor());

  if (configService.get<string>('APP_ENV') !== 'prod') {
    const config = new DocumentBuilder()
      .setTitle('API example')
      .setDescription('The API description')
      .setVersion('1.0')
      .addTag('api')
      .build();
    const documentFactory = () => SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('los/api-docs', app, documentFactory);
  }
  return app;
}
