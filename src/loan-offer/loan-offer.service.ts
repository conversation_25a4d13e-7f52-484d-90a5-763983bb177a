import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { LoanOffer, LoanOfferType } from './entity/loan-offer.entity';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';
import { IncomeVerification } from 'src/income-verification/entities/income-verification.entity';
import { Address, AddressType } from 'src/addresses/entity/address.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { CalculateEmiDto } from './dto/calculate-emi.dto';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';

interface UnderwritingPayload {
  age: number;
  salary: number;
  // tenure: number;
  cibilScore: string;
  existingEmi: number;
  // otherIncome: number;
  maritalStatus: string;
  residenceTypes: string;
  employerCategory: string;
  // appliedLoanAmount: number;
  highestQualification: string;
  limitUtilizationRange: number;
  newAccountsLastXMonths: number;
  noOfCreditEnquiriesRange: number;
  monthsInCurrentEmployment: number;
  totalWorkExperienceMonths: number;
  newDelinquentAccountsLastXMonths: number;
  paymentsReportedOnTimeRatioRange: number;
}

@Injectable()
export class LoanOfferService {
  private readonly logger = new Logger(LoanOfferService.name);
  private readonly authServiceBaseUrl: string;
  private readonly validityOfLoanOffer: number;

  constructor(
    @InjectRepository(LoanOffer)
    private readonly loanOfferRepo: Repository<LoanOffer>,
    @InjectRepository(IncomeVerification)
    private readonly incomeVerificationRepo: Repository<IncomeVerification>,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepo: Repository<LoanApplication>,
    @InjectRepository(Address)
    private readonly addressRepo: Repository<Address>,
    private readonly cls: ClsService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.authServiceBaseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
    this.validityOfLoanOffer = this.configService.getOrThrow<number>('VALIDITY_OF_LOAN_OFFER');
  }

  async getLatestOfferByApplication(applicationId: number): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const latestOffer = await this.loanOfferRepo
        .createQueryBuilder('offer')
        .innerJoin('offer.application', 'application')
        .where('application.id = :applicationId', { applicationId })
        .andWhere('application.userId = :userId', { userId })
        .andWhere('application.statusId = 2')
        .andWhere('offer.type = :offerType', { offerType: LoanOfferType.CAD_PROPOSED })
        .orderBy('offer.createdAt', 'DESC')
        .limit(1)
        .getOne();

      if (!latestOffer) {
        return {
          success: false,
          error: 'Unauthorized or invalid application ID.',
        };
      }

      const diffDays = Math.floor(
        (Date.now() - new Date(latestOffer.createdAt).getTime()) / 86400000
      );

      if (diffDays > this.validityOfLoanOffer) {
        const { affected } = await this.loanApplicationRepo.update(applicationId, {
          subStatus: ApplicationSubStatus.OFFER_EXPIRED,
          statusId: 7,
        });

        if (!affected) {
          return {
            success: true,
            data: { isExpired: true },
            message: 'Loan offer expired but application status update failed',
          };
        }
        return {
          success: true,
          data: { isExpired: true },
          message: 'Loan offer expired please reapply',
        };
      }

      return {
        success: true,
        data: { isExpired: false, ...latestOffer },
        message: 'Latest loan offer fetched successfully',
      };
    } catch (error) {
      this.logger.error('Error in getLatestOfferByApplication:', error);
      return {
        success: false,
        error: error.message ?? 'Internal server error',
      };
    }
  }

  async getEligibility(): Promise<Message> {
    this.logger.log('Hitting getEligibility Service API');

    const userId = this.cls.get<number>('userId');

    const application = await this.loanApplicationRepo.findOne({
      where: {
        userId: userId,
        statusId: 1,
        subStatus: ApplicationSubStatus.INCOME_VERIFICATION,
      },
      order: {
        createdAt: 'DESC',
      },
      relations: ['applicantDetail'],
    });

    if (!application) {
      return { success: false, error: 'No active application found for the user.' };
    }

    const isLoanOfferExists = await this.loanOfferRepo.findOne({
      where: { applicationId: application.id, type: LoanOfferType.DEFAULT },
    });

    this.logger.log('isLoanOfferExists>>', isLoanOfferExists);

    if (isLoanOfferExists) {
      const diffDays = Math.floor(
        (Date.now() - new Date(isLoanOfferExists.createdAt).getTime()) / 86400000
      );

      if (diffDays > this.validityOfLoanOffer) {
        const { affected } = await this.loanApplicationRepo.update(application.id, {
          subStatus: ApplicationSubStatus.OFFER_EXPIRED,
          statusId: 7,
        });

        if (!affected) {
          throw new Error('Loan Offer Expired but application status update failed');
        }
        return { success: false, error: 'Loan Offer Expired' };
      }

      return {
        success: true,
        data: {
          ...isLoanOfferExists,
        },
        message: 'Eligibility data fetched successfully',
      };
    }
    const applicantDetail = application?.applicantDetail;

    if (!applicantDetail) {
      throw new Error('Applicant details not found for the application.');
    }

    this.logger.debug('Application:', application);

    const incomeVerification = await this.incomeVerificationRepo.findOne({
      where: { applicationId: applicantDetail.applicationId, isTxnCompleted: true },
      order: { createdAt: 'DESC' },
    });

    if (!incomeVerification) {
      throw new Error('Income verification details not found for the application.');
    }

    const address = await this.addressRepo.findOne({
      where: { entityId: userId, entityType: 'user', type: AddressType.CURRENT, isVerified: true },
    });

    if (!address) {
      throw new Error('Address details not found for the user.');
    }

    this.logger.debug('Address:', address);

    const dob = applicantDetail?.dob ? new Date(applicantDetail.dob) : null;
    const today = new Date();

    const age = dob ? today.getFullYear() - dob.getFullYear() : 0;

    const residenceType = address?.accommodationType;

    let transactionResponse: any;
    try {
      this.logger.debug('data is >>>', age, residenceType);

      const payload: UnderwritingPayload = {
        age: Number(age),
        salary: applicantDetail.monthlyIncome!,
        // tenure: 36, // remove
        cibilScore: applicantDetail.creditAnalysis?.creditScore ?? '755',
        existingEmi: 0,
        // otherIncome: 20000, //remove
        maritalStatus: applicantDetail.maritalStatus,
        residenceTypes: residenceType!,
        employerCategory: applicantDetail.organisationType ?? 'PPP',
        // appliedLoanAmount: 300000, //remove
        highestQualification: applicantDetail.education,
        limitUtilizationRange: applicantDetail.creditAnalysis?.limitUtilization ?? 0,
        newAccountsLastXMonths: applicantDetail.creditAnalysis?.newAccountsMonths ?? 0,
        noOfCreditEnquiriesRange: applicantDetail.creditAnalysis?.creditEnquiries ?? 0,
        monthsInCurrentEmployment: applicantDetail.currentExp ?? 40,
        totalWorkExperienceMonths: applicantDetail.totalExp ?? 65,
        newDelinquentAccountsLastXMonths:
          applicantDetail?.creditAnalysis?.newDelinquentAccountsCount ?? 0,
        paymentsReportedOnTimeRatioRange: applicantDetail.creditAnalysis?.paymentOnTimeRatio ?? 90,
      };

      this.logger.log('Underwriting Payload:', payload);
      const url = `${this.authServiceBaseUrl}/underwriting/api/underwriting/execute-rule/1`;
      const header = this.cls.get<Record<string, string>>('header');

      type UnderwritingResponse = {
        message: string;
        data: {
          approvedLoanAmount: number;
          tenure: number;
          emi: number;
          applicableInterestRate: number;
          airpayScore: number;
          snapId: string;
          finalEligibility: { status: boolean };
          metadata: { underwritingResponse: { [key: string]: any } };
        };
      };
      this.logger.log('Calling underwriting execute-rule API ');
      const { data } = await firstValueFrom(
        this.httpService.post<UnderwritingResponse>(url, payload, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${header?.authorization}`,
          },
        })
      );

      this.logger.log('Underwriting  response:', data.message);
      const resData = data.data;
      const defaultEligibilityPayload = this.loanOfferRepo.create({
        applicationId: application.id,
        type: LoanOfferType.DEFAULT,
        amount: +resData.approvedLoanAmount,
        tenureInMonths: +resData.tenure,
        emi: +resData.emi,
        roi: +resData.applicableInterestRate,
        airpayScore: +resData.airpayScore,
        snapshotId: resData.snapId,
        finalEligibility: resData.finalEligibility.status,
        metadata: {
          underwritingResponse: resData,
        },
      });

      transactionResponse = await this.dataSource.transaction(async (manager) => {
        this.logger.debug('final eligibility status >>', resData.finalEligibility.status);
        if (resData.finalEligibility.status == false) {
          await manager.update(LoanApplication, application.id, {
            subStatus: ApplicationSubStatus.UNDERWRITING_REJECTED,
            statusId: 7,
          });
          const frozenTillDate: Date = new Date();
          frozenTillDate.setDate(frozenTillDate.getDate() + 90);
          await manager.save(FrozenAccount, {
            pan: applicantDetail.pan,
            userId: application.userId,
            frozenTillDate,
            reason: `Application #${application.id} Rejected`,
          });
        }
        const savedDefaultEligibility = await manager.save(LoanOffer, defaultEligibilityPayload);

        return savedDefaultEligibility;
      });
    } catch (error) {
      this.logger.debug('err> ', error);
      return {
        success: false,
        error: error.message ?? 'Failed to fetch eligibility data',
      };
    }

    return {
      success: true,
      data: transactionResponse,
      message: 'Eligibility data fetched successfully',
    };
  }

  async calculateEmi(dto: CalculateEmiDto): Promise<Message> {
    return this.emiCalculator(dto, false);
  }

  async saveCalculateEmiByUser(dto: CalculateEmiDto): Promise<Message> {
    return this.emiCalculator(dto, true);
  }

  async emiCalculator(dto: CalculateEmiDto, persist = false): Promise<Message> {
    this.logger.log('Hitting calculateEmi Service ');

    const userId = this.cls.get<number>('userId');

    const offerRes: any = await this.loanOfferRepo
      .createQueryBuilder('offer')
      .innerJoin('offer.application', 'application')
      .innerJoin('application.lendingPartner', 'lendingPartner')
      .innerJoin('lendingPartner.partnerProductMappings', 'partnerProductMappings')
      .select([
        'offer.type as type',
        'offer.createdAt as createdat',
        'offer.snapshotId as snapshotid ',
        'partnerProductMappings.rulesetId as rulesetid ',
        'application.id as applicationid',
      ])
      .where('offer.type = :offerType', { offerType: LoanOfferType.CAD_PROPOSED })
      .andWhere('offer.id = :offerId', { offerId: dto.offerId })
      .andWhere('application.userId = :userId', { userId })
      .limit(1)
      .getRawMany();

    if (offerRes.length == 0) {
      throw new Error('No loan offer found for the given offer ID.');
    }

    const diffDays = Math.floor(
      (Date.now() - new Date(offerRes[0].createdat).getTime()) / 86400000
    );

    if (diffDays > this.validityOfLoanOffer) {
      const { affected } = await this.loanApplicationRepo.update(+offerRes[0].applicationid, {
        subStatus: ApplicationSubStatus.OFFER_EXPIRED,
        statusId: 7,
      });

      if (!affected) {
        return {
          success: true,
          data: { isExpired: true },
          message: 'Loan offer expired but application status update failed',
        };
      }

      return {
        success: true,
        data: { isExpired: true },
        message: 'Loan offer expired please reapply',
      };
    }

    this.logger.log('Underwriting calculatemi snapshotId>>', offerRes);
    const snapshotId = offerRes[0].snapshotid;

    const url = `${this.authServiceBaseUrl}/underwriting/api/underwriting/calculate-emi/${offerRes[0].rulesetid}`;

    const header = this.cls.get('header');

    this.logger.log('Calling underwriting execute-rule API ');

    const res = await firstValueFrom(
      this.httpService.post(
        url,
        { uuid: `${snapshotId}`, appliedLoanAmount: dto.amount, tenure: dto.tenure },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${header?.authorization}`,
          },
        }
      )
    );

    this.logger.debug('Underwriting calculateEmi response: >', res.data, res.status);

    if (persist) {
      const defaultEligibility = this.loanOfferRepo.create({
        applicationId: +offerRes[0].applicationid,
        type: LoanOfferType.USER_ACCEPTED,
        amount: +res.data.data.approvedLoan,
        tenureInMonths: +dto.tenure,
        emi: +res.data.data.emi,
        roi: +res.data.data.interestRate,
        airpayScore: +res.data.data.airpayScore,
        snapshotId: snapshotId,
      });

      await this.loanOfferRepo.save(defaultEligibility);
    }

    return {
      success: true,
      data: { isExpired: false, ...res.data.data },
      message: 'CalculateEmi data fetched successfully',
    };
  }

  async acknowledgeEligibility(offerId: number): Promise<Message> {
    this.logger.log('Hitting acknowledgeEligibility Service API');

    const offer = await this.loanOfferRepo.findOne({
      where: { id: offerId },
      select: ['id', 'applicationId'],
    });

    if (!offer) {
      return {
        success: false,
        error: `LoanOffer with ID ${offerId} not found`,
      };
    }

    this.logger.log(`Offer found with applicationId: ${offer.applicationId} and offerId`, offer.id);

    const userId = this.cls.get<number>('userId');

    const result = await this.loanApplicationRepo.update(
      { id: offer.applicationId, userId },
      { subStatus: ApplicationSubStatus.ELIGIBILITY_SHOWN }
    );

    if (result.affected && result.affected > 0) {
      return {
        success: true,
        data: { updatedId: offer.applicationId },
        message: 'ApplicationSubStatus updated successfully',
      };
    }

    return {
      success: false,
      data: null,
      message: 'Update failed no rows were affected',
    };
  }
}
