import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoanOffer } from './entity/loan-offer.entity';
import { LoanOfferService } from './loan-offer.service';
import { LoanOfferController } from './loan-offer.controller';
import { LoanApplicationModule } from 'src/loan-application/loan-application.module';
import { IncomeVerification } from 'src/income-verification/entities/income-verification.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { HttpModule } from '@nestjs/axios';
import { LoanOfferAdminController } from './admin/loan-offer-admin.controller';
import { LoanOfferAdminService } from './admin/loan-offer-admin.service';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([LoanOffer, IncomeVerification, Address, LoanApplication]),
    LoanApplicationModule,
    HttpModule,
  ],
  providers: [LoanOfferService, LoanOfferAdminService],
  controllers: [LoanOfferController, LoanOfferAdminController],
})
export class LoanOfferModule {}
