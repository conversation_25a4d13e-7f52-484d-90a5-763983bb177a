import { Entity, PrimaryGeneratedColumn, Column, <PERSON>To<PERSON>ne, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, Check } from 'typeorm';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

export enum LoanOfferType {
  DEFAULT = 'default',
  CAD_PROPOSED = 'cad_proposed',
  USER_ACCEPTED = 'user_accepted',
}

@Entity({ name: 'loan_offers' })
@Check(`"amount" > 0`)
@Check(`"roi" > 0`)
@Check(`"tenure_in_months" > 0`)
export class LoanOffer extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @ManyToOne(() => LoanApplication, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Column({
    name: 'type',
    type: 'enum',
    enum: LoanOfferType,
    default: LoanOfferType.DEFAULT,
  })
  type: LoanOfferType;

  @Column({ name: 'amount', type: 'decimal', precision: 9, scale: 2 })
  amount: number;

  @Column({ name: 'tenure_in_months', type: 'int' })
  tenureInMonths: number;

  @Column({ name: 'emi', type: 'decimal', precision: 9, scale: 2 })
  emi: number;

  @Column({ name: 'roi', type: 'decimal', precision: 5, scale: 2 })
  roi: number;

  @Column({ name: 'airpay_score', type: 'decimal', precision: 4, scale: 2 })
  airpayScore: number;

  @Column({ name: 'snapshot_id' })
  snapshotId: string;

  @Column({ name: 'final_eligibility', default: false })
  finalEligibility: boolean;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;
}
