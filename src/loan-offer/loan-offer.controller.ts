import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  UseGuards,
} from '@nestjs/common';
import { LoanOfferService } from './loan-offer.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { CalculateEmiDto } from './dto/calculate-emi.dto';

@UseGuards(DecodeTokenGuard)
@Controller('loan-offer')
export class LoanOfferController {
  private readonly logger = new Logger(LoanOfferController.name);
  constructor(private readonly loanOfferService: LoanOfferService) {}

  @Get('latest/:applicationId')
  async getLatestOffer(@Param('applicationId', ParseIntPipe) applicationId: number) {
    return fixed_response(await this.loanOfferService.getLatestOfferByApplication(applicationId));
  }

  @Get('eligibility')
  async getEligibility() {
    return fixed_response(await this.loanOfferService.getEligibility());
  }

  @Post('calculate-emi')
  async calculateEmi(@Body() dto: CalculateEmiDto) {
    this.logger.log('Hitting calculateEmi Controller');
    return fixed_response(await this.loanOfferService.calculateEmi(dto));
  }

  @Post()
  async saveCalculateEmiByUser(@Body() dto: CalculateEmiDto) {
    return fixed_response(await this.loanOfferService.saveCalculateEmiByUser(dto));
  }

  @Get('acknowledge-eligibility/:offerId')
  async acknowledgeEligibility(@Param('offerId', ParseIntPipe) offerId: number) {
    this.logger.log('Hitting acknowledgeEligibility Controller');
    return fixed_response(await this.loanOfferService.acknowledgeEligibility(offerId));
  }
}
