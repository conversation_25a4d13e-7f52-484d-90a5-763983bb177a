import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/role.decorator';
import { LoanOfferAdminService } from './loan-offer-admin.service';
import { CalculateEmiDto } from '../dto/calculate-emi.dto';

@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('credit-officer')
@Controller('admin/loan-offer')
export class LoanOfferAdminController {
  constructor(private readonly adminLoanAppService: LoanOfferAdminService) {}

  @Post('calculate-emi')
  async approveLoan(@Body() dto: CalculateEmiDto) {
    return fixed_response(await this.adminLoanAppService.calculateEmi(dto));
  }

  @Post()
  async saveCalculateEmiByCad(@Body() dto: CalculateEmiDto) {
    return fixed_response(await this.adminLoanAppService.saveCalculateEmiByCad(dto));
  }
}
