import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { LoanOffer, LoanOfferType } from '../entity/loan-offer.entity';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { CalculateEmiDto } from '../dto/calculate-emi.dto';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { ApplicationRemarks } from 'src/application-event-log/entity/application-remarks.entity';

@Injectable()
export class LoanOfferAdminService {
  private readonly logger = new Logger(LoanOfferAdminService.name);
  private readonly authServiceBaseUrl: string;

  constructor(
    @InjectRepository(LoanOffer)
    private readonly loanOfferRepo: Repository<LoanOffer>,
    private readonly cls: ClsService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.authServiceBaseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
  }

  async calculateEmi(dto: CalculateEmiDto): Promise<Message> {
    return this.emiCalculator(dto, false);
  }

  async saveCalculateEmiByCad(dto: CalculateEmiDto): Promise<Message> {
    return this.emiCalculator(dto, true);
  }

  private async emiCalculator(dto: CalculateEmiDto, persist = false) {
    const offerRes = await this.loanOfferRepo
      .createQueryBuilder('offer')
      .innerJoin('offer.application', 'application')
      .innerJoin('application.lendingPartner', 'lendingPartner')
      .innerJoin('lendingPartner.partnerProductMappings', 'partnerProductMappings')
      .select([
        'offer.type as type',
        'offer.snapshot_id as snapshotId',
        'partnerProductMappings.rulesetId as rulesetId',
        'application.id as applicationid',
      ])
      .where('offer.type = :offerType', { offerType: LoanOfferType.DEFAULT })
      .andWhere('offer.id = :offerId', { offerId: dto.offerId })
      .limit(1)
      .getRawMany();

    const snapshotId = offerRes[0].snapshotid;

    this.logger.debug('Underwriting calculatemi snapshotId >>', snapshotId);

    const url = `${this.authServiceBaseUrl}/underwriting/api/underwriting/calculate-emi/${offerRes[0].rulesetid}`;
    const header = this.cls.get<Record<string, string>>('header');
    this.logger.log('Calling underwriting execute-rule API ');
    const res = await firstValueFrom(
      this.httpService.post(
        url,
        { uuid: `${snapshotId}`, appliedLoanAmount: dto.amount, tenure: dto.tenure },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${header?.authorization}`,
          },
        }
      )
    );
    this.logger.debug('Underwriting calculateEmi response: >', res.data, res.status);
    const applicationId = +offerRes[0].applicationid;
    if (persist) {
      const defaultEligibility = this.loanOfferRepo.create({
        applicationId,
        type: LoanOfferType.CAD_PROPOSED,
        amount: +res.data.data.approvedLoan,
        tenureInMonths: +dto.tenure,
        emi: +res.data.data.emi,
        roi: +res.data.data.interestRate,
        airpayScore: +res.data.data.airpayScore,
        snapshotId: snapshotId,
      });

      await this.dataSource.transaction(async (manager) => {
        await manager.save(LoanOffer, defaultEligibility);
        await manager.update(LoanApplication, applicationId, {
          subStatus: ApplicationSubStatus.CREDIT_OFFICER_APPROVED,
          statusId: 2,
        });

        await manager.save(ApplicationRemarks, {
          applicationId,
          remarks: dto.remarks,
          action: 'CREDIT_OFFICER_APPROVAL',
          userRole: 'credit-officer',
        });

        await manager.save(ApplicationEventLog, {
          applicationId,
          eventType: ApplicationSubStatus.CREDIT_OFFICER_APPROVED,
        });
      });
    }

    return {
      success: true,
      data: { ...res.data.data },
      message: 'CalculateEmi data fetched and saved successfully',
    };
  }
}
