import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { ApplicantDetail } from './entity/applicant-details.entity';
import { CreateApplicantDetailsDto } from './dto/applicant-details.dto';
import { Address, AddressType } from 'src/addresses/entity/address.entity';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { isUUID } from 'class-validator';

@Injectable()
export class ApplicantDetailsService {
  private readonly logger = new Logger(ApplicantDetailsService.name);
  private baseUrl;

  constructor(
    @InjectRepository(ApplicantDetail)
    private readonly applicantRepo: Repository<ApplicantDetail>,

    @InjectRepository(Address)
    private readonly addressRepo: Repository<Address>,
    private readonly cls: ClsService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.baseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
  }

  async applicantDetailsUpdate(
    dto: CreateApplicantDetailsDto,
    applicationId: number
  ): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const applicant = await this.applicantRepo
        .createQueryBuilder('applicant')
        .innerJoinAndSelect('applicant.application', 'application')
        .where('application.id = :applicationId', { applicationId: applicationId })
        .andWhere('application.userId = :userId', { userId })
        .getOne();

      if (!applicant) {
        return {
          success: false,
          error: 'Applicant details not found or unauthorized access.',
        };
      }

      const updatePayload: Record<string, string | boolean | number | undefined> = {
        companyName: dto.companyName,
        currentExp: dto.currentExp,
        totalExp: dto.totalExp,
        organisationType: dto.organisationType,
      };

      // here assumption is workEmail is only populated if it is verified, so in case of update, either FE will send same email then nothing to be done
      // else verify with auth service.
      if (dto.workEmail && applicant.workEmail !== dto.workEmail) {
        if (!isUUID(dto.workEmail)) {
          return {
            success: false,
            error: 'Invalid work email format.',
          };
        }
        let emails: { workEmail?: string; personalEmail?: string } = {};
        type AuthProfileResponse = {
          success: boolean;
          data: {
            workEmail: string;
            personalEmail: string;
          };
        };
        const response = await firstValueFrom(
          this.httpService.post<AuthProfileResponse>(
            `${this.baseUrl}/auth/api/get-emails`,
            { identifier: dto.workEmail },
            {
              headers: {
                Authorization: this.cls.get<Record<string, string>>('header')['authorization'],
              },
            }
          )
        );
        this.logger.debug('email api data:', response.data);
        if (response.data.success) emails = response.data.data;

        if (emails.workEmail) {
          updatePayload.workEmail = emails.workEmail;
          updatePayload.isWorkEmailVerified = true;
          updatePayload.email = emails.personalEmail;
        }
      }

      if (applicant.application.productId === 1) {
        updatePayload['employmentType'] = 'SALARIED';
      }

      await this.dataSource.transaction(async (manager) => {
        await manager.update(ApplicantDetail, applicant.id, updatePayload);
        await manager.update(LoanApplication, applicationId, {
          subStatus: ApplicationSubStatus.WORK_DETAILS,
        });

        await manager.save(ApplicationEventLog, {
          applicationId,
          eventType: ApplicationSubStatus.WORK_DETAILS,
        });
      });

      const applicantId = applicant.id;
      const existingAddress = await this.addressRepo.findOne({
        where: {
          type: AddressType.OFFICE,
          entityType: 'user',
          entityId: userId,
          linkedEntityType: 'applicant_details',
          linkedEntityId: applicantId,
        },
      });

      if (existingAddress) {
        await this.addressRepo.softRemove(existingAddress);
      }

      const newAddress = this.addressRepo.create({
        addressLine1: dto.addressLine1,
        addressLine2: dto.addressLine2,
        pincode: dto.pincode,
        city: dto.city,
        state: dto.state,
        type: AddressType.OFFICE,
        entityType: 'user',
        entityId: userId,
        linkedEntityType: 'applicant_details',
        linkedEntityId: applicantId,
      });

      await this.addressRepo.save(newAddress);

      return {
        success: true,
        data: {
          message: 'Applicant details updated successfully and new address created',
          id: applicantId,
        },
      };
    } catch (error) {
      this.logger.error('Error in createOrUpdate ApplicantDetails:', error);
      return {
        success: false,
        error: error.message ?? 'Internal server error',
      };
    }
  }

  async getDetailsProfile(): Promise<Message> {
    const userId = this.cls.get<number>('userId');
    const record = await this.applicantRepo
      .createQueryBuilder('applicant')
      .innerJoinAndSelect('applicant.application', 'application')
      .leftJoinAndMapMany(
        'applicant.addresses',
        'addresses',
        'address',
        `address.type = :addrType and address.linkedEntityType = :linkedEntityType and address.linkedEntityId = applicant.id`,
        {
          addrType: 'OFFICE',
          linkedEntityType: 'applicant_details',
        }
      )
      .where('application.userId = :userId', { userId })
      .orderBy('application.createdAt', 'DESC')
      .getOne();

    if (record) {
      return {
        success: true,
        data: record,
        message: 'Applicant details found',
      };
    }
    return {
      success: false,
      error: 'Applicant details not found',
    };
  }
}
