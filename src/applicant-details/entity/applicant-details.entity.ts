// applicant_details.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  RelationId,
  Index,
} from 'typeorm';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { EducationalQualification } from 'src/common/enums/education.enum';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

export enum MaritalStatus {
  SINGLE = 'SINGLE',
  MARRIED = 'MARRIED',
  DIVORCED = 'DIVORCED',
  WIDOWED = 'WIDOWED',
}

export enum OrganisationType {
  PVT_LTD_CO = 'PVT_LTD_CO',
  PROPRIETORSHIP = 'PROPRIETORSHIP',
  PARTNERSHIP = 'PARTNERSHIP',
  LIMITED_CO = 'LIMITED_CO',
  LLP = 'LLP',
}

@Entity({ name: 'applicant_details' })
export class ApplicantDetail extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => LoanApplication, (loanApp) => loanApp.id)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @RelationId((applicant: ApplicantDetail) => applicant.application)
  @Index()
  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @Column()
  name: string;

  @Column({ type: 'date' })
  dob: string;

  @Column({ type: 'char', enum: ['M', 'F', 'X'] })
  gender: string;

  @Column({ length: 10 })
  pan: string;

  @Column({ type: 'varchar', length: 12, name: 'mobile' })
  mobile: string;

  @Column({ name: 'employment_type', type: 'varchar', nullable: true })
  employmentType: string | null;

  @Column({ name: 'salary_disbursement_mode', type: 'varchar', nullable: true })
  salaryDisbursementMode: string | null;

  @Column({
    name: 'marital_status',
    type: 'enum',
    enum: MaritalStatus,
  })
  maritalStatus: MaritalStatus;

  @Column({ name: 'dependents_count', type: 'int', nullable: true })
  dependentsCount: number | null;

  @Column({ type: 'enum', enum: EducationalQualification })
  education: EducationalQualification;

  @Column({ name: 'company_name', type: 'varchar', nullable: true })
  companyName: string | null;

  @Column({ name: 'current_exp', type: 'decimal', precision: 5, scale: 2, nullable: true })
  currentExp: number | null;

  @Column({ name: 'total_exp', type: 'decimal', precision: 5, scale: 2, nullable: true })
  totalExp: number | null;

  @Column({ name: 'work_email', type: 'varchar', nullable: true })
  workEmail: string | null;

  @Column({ name: 'is_work_email_verified', type: 'boolean', default: false })
  isWorkEmailVerified: boolean;

  @Column({ name: 'monthly_income', type: 'decimal', precision: 10, scale: 2, nullable: true })
  monthlyIncome: number | null;

  @Column({
    name: 'credit_analysis',
    type: 'jsonb',
    nullable: true,
    default: null,
  })
  creditAnalysis: Record<string, any>;

  @Column({
    name: 'organisation_type',
    type: 'enum',
    enum: OrganisationType,
    nullable: true,
  })
  organisationType: OrganisationType;

  @Column({ name: 'email', nullable: true })
  email: string;

  @Column({ name: 'kyc_verified', default: false })
  kycVerified: boolean;
}
