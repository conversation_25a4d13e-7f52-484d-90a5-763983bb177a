import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicantDetail } from './entity/applicant-details.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { ApplicantDetailsService } from './applicant-details.service';
import { ApplicantDetailsController } from './applicant-details.controller';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicantDetail, Address]), HttpModule],
  providers: [ApplicantDetailsService],
  controllers: [ApplicantDetailsController],
  exports: [ApplicantDetailsService],
})
export class ApplicantDetailsModule {}
