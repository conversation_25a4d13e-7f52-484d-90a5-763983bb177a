import { Controller, Put, Body, ParseIntPipe, Query, UseGuards, Get } from '@nestjs/common';
import { ApplicantDetailsService } from './applicant-details.service';
import { CreateApplicantDetailsDto } from './dto/applicant-details.dto';
import { Message, fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('applicant-details')
export class ApplicantDetailsController {
  constructor(private readonly applicantDetailsService: ApplicantDetailsService) {}

  @Put()
  async updateApplicant(
    @Query('applicationId', ParseIntPipe) applicationId: number,
    @Body() dto: CreateApplicantDetailsDto
  ): Promise<Message> {
    return fixed_response(
      await this.applicantDetailsService.applicantDetailsUpdate(dto, applicationId)
    );
  }

  @Get()
  async getMyDetails() {
    return fixed_response(await this.applicantDetailsService.getDetailsProfile());
  }
}
