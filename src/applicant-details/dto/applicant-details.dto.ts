import { IsNotEmpty, IsOptional, IsString, IsN<PERSON>ber, IsEnum } from 'class-validator';
import { OrganisationType } from '../entity/applicant-details.entity';

export class CreateApplicantDetailsDto {
  @IsOptional()
  @IsString()
  companyName?: string;

  @IsOptional()
  @IsNumber()
  currentExp?: number;

  @IsOptional()
  @IsNumber()
  totalExp?: number;

  @IsOptional()
  workEmail?: string;

  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @IsOptional()
  @IsString()
  addressLine2?: string;

  @IsString()
  @IsNotEmpty()
  pincode: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsNotEmpty()
  @IsEnum(OrganisationType)
  organisationType: OrganisationType;
}
