import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
export class LoanStatusMaster extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  label: string;

  @Column({ type: 'varchar' })
  code: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'is_terminal', type: 'boolean', default: false })
  isTerminal: boolean;
}
