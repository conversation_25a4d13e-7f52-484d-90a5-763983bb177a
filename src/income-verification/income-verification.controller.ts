import { Controller, Post, Body, Logger, UseGuards } from '@nestjs/common';
import { IncomeVerificationService } from './income-verification.service';
import { IncomeVerificationInitiateDto } from './dto/income-verification-initiate.dto';
import { FetchIncomeReportDto } from './dto/fetch-income-report.dto';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { fixed_response } from 'src/utils/response-util';

@UseGuards(DecodeTokenGuard)
@Controller()
export class IncomeVerificationController {
  private readonly logger = new Logger(IncomeVerificationController.name);

  constructor(private readonly incomeVerificationService: IncomeVerificationService) {}

  @Post('initiate-income-verification')
  async incomeVerificationInitiate(@Body() dto: IncomeVerificationInitiateDto) {
    this.logger.log('Hitting IncomeVarificationInitiate API');

    return fixed_response(await this.incomeVerificationService.initiateIncomeVerification(dto));
  }

  @Post('fetch-income-report')
  async fetchIncomeReport(@Body() dto: FetchIncomeReportDto) {
    this.logger.log('Hitting fetchIncomeReport API');

    return fixed_response(await this.incomeVerificationService.fetchIncomeReport(dto));
  }
}
