import { Module } from '@nestjs/common';
import { IncomeVerificationService } from './income-verification.service';
import { IncomeVerificationController } from './income-verification.controller';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IncomeVerification } from './entities/income-verification.entity';
import { LoanApplicationModule } from 'src/loan-application/loan-application.module';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([IncomeVerification, LoanApplication]),
    LoanApplicationModule,
  ],
  controllers: [IncomeVerificationController],
  providers: [IncomeVerificationService],
})
export class IncomeVerificationModule {}
