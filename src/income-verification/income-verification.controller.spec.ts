import { Test, TestingModule } from '@nestjs/testing';
import { IncomeVarificationController } from './income-verification.controller';
import { IncomeVarificationService } from './income-verification.service';

describe('IncomeVarificationController', () => {
  let controller: IncomeVarificationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [IncomeVarificationController],
      providers: [IncomeVarificationService],
    }).compile();

    controller = module.get<IncomeVarificationController>(IncomeVarificationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
