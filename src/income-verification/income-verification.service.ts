import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IncomeVerificationInitiateDto } from './dto/income-verification-initiate.dto';
import { ClsService } from 'nestjs-cls';
import { firstValueFrom } from 'rxjs';
import { FetchIncomeReportDto } from './dto/fetch-income-report.dto';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { IncomeVerification } from './entities/income-verification.entity';
import { DataSource, Repository } from 'typeorm';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { Message } from 'src/utils/response-util';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';

@Injectable()
export class IncomeVerificationService {
  private readonly logger = new Logger(IncomeVerificationService.name);
  private readonly kycServiceBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly cls: ClsService,
    @InjectRepository(IncomeVerification)
    private readonly incomeVerificationRepository: Repository<IncomeVerification>,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepository: Repository<LoanApplication>,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.kycServiceBaseUrl = this.configService.getOrThrow<string>('KYC_SERVICE_BASE_URL');
  }

  async initiateIncomeVerification(dto: IncomeVerificationInitiateDto): Promise<Message> {
    this.logger.log('Initiating income verification');
    const userId = this.cls.get<number>('userId');
    const application = await this.loanApplicationRepository.findOne({
      relations: ['applicantDetail'],
      where: {
        userId: userId,
        statusId: 1,
        subStatus: ApplicationSubStatus.CIBIL_ACCEPTED,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (!application) {
      throw new Error(`No application found awaiting income verification`);
    }

    const isTxnInitiated = await this.incomeVerificationRepository.findOne({
      where: { applicationId: application.id, isTxnCompleted: true },
    });

    if (isTxnInitiated) {
      this.logger.warn(`Transaction already completed for applicationId ${application.id}`);
      return {
        success: true,
        message: 'Transaction already completed for this application',
        data: { skip: true },
      };
    }

    const mobileNo = application.applicantDetail.mobile;
    const newMobileNo = dto.mobileNo ?? mobileNo;
    const body = { mobileNo: newMobileNo };
    this.logger.debug(`Mobile number for income verification: ${newMobileNo}`);

    const url = `${this.kycServiceBaseUrl}/api/account-aggregator-initiate-consent`;

    const header = this.cls.get('header');

    this.logger.debug('Calling KYC aaIntiateConsent API');

    const res = await firstValueFrom(
      this.httpService.post(url, body, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `${header?.authorization}`,
        },
      })
    );

    if (!res.data?.success) {
      throw new Error('Failed to initiate income verification');
    }

    const reportEntity = this.incomeVerificationRepository.create({
      applicationId: application.id,
      txnId: String(res.data.data.TxnID),
    });

    await this.incomeVerificationRepository.save(reportEntity);

    let callbackUrl;
    if (this.configService.get('APP_ENV') === 'production') {
      callbackUrl = 'https://baas.airpay.co.in/aa-consent-returnurl.php';
    }
    callbackUrl = 'https://baas.airpay.ninja/aa-consent-returnurl.php';

    return {
      success: true,
      message: 'Income verification initiated successfully',
      data: { ...res.data.data, callbackUrl },
    };
  }

  async fetchIncomeReport(dto: FetchIncomeReportDto): Promise<Message> {
    this.logger.log('Hitting fetchIncomeReport Service');

    const userId = this.cls.get<number>('userId');
    const application = await this.loanApplicationRepository.findOne({
      where: {
        userId,
        statusId: 1,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (!application) {
      throw new Error(`Application not found for given userId`);
    }
    const isTxnInitiated = await this.incomeVerificationRepository.findOne({
      where: { txnId: dto.txnId, applicationId: application.id },
    });

    if (!isTxnInitiated) {
      throw new Error(`Transaction not initiated for txnId ${dto.txnId}`);
    }

    if (isTxnInitiated.isTxnCompleted) {
      return {
        success: true,
        data: { retry: false },
        message: 'Income report already fetched',
      };
    }

    const body = { txnId: dto.txnId };
    const txnStatusUrl = `${this.kycServiceBaseUrl}/api/account-aggregator-transaction-status`;
    const fetchReportUrl = `${this.kycServiceBaseUrl}/api/account-aggregator-report`;

    const header = this.cls.get('header');

    const maxRetries = 3;
    let status;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      this.logger.debug('Calling KYC transaction-status API');
      const txnStatusResponse = await firstValueFrom(
        this.httpService.post(txnStatusUrl, body, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${header?.authorization}`,
          },
        })
      );

      status = txnStatusResponse.data?.data?.status;
      const success = txnStatusResponse.data?.data?.success;
      const errorCode = txnStatusResponse.data?.data?.errorCode;

      this.logger.debug('txnStatusResponse>>', txnStatusResponse.data);

      if (
        !['COMPLETED', 'DATA_FETCH_PENDING', 'PROCESSING_DATA', 'DATA_FETCH_IN_PROGRESS'].includes(
          status
        )
      ) {
        this.logger.error('transaction api error', status);
        throw new Error(`Transaction status failed says ${status}`);
      }

      if (status === 'COMPLETED' && success && errorCode === 'NONE') {
        this.logger.log('Calling KYC Report API');
        const reportResponse = await firstValueFrom(
          this.httpService.post(fetchReportUrl, body, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `${header?.authorization}`,
            },
          })
        );

        if (!reportResponse.data.success) {
          this.logger.debug('reportResponse >>', reportResponse.data);
          throw new Error(
            `${reportResponse.data.error.errorCode} and ${reportResponse.data.error.errorMessage}` ||
              'KYC Report API call failed'
          );
        }
        if (reportResponse.data?.success) {
          this.logger.debug('reportResponse>>', reportResponse.data);
          await this.dataSource.transaction(async (manager) => {
            await manager.update(
              IncomeVerification,
              { txnId: dto.txnId },
              {
                isTxnCompleted: reportResponse.data.success,
                response: reportResponse.data.data,
              }
            );

            await manager.insert(BankAccount, {
              entityType: 'user',
              entityId: userId,
              linkedEntityType: 'loan_applications',
              linkedEntityId: application.id,
              bankName: reportResponse.data.data.customerInfo.bank,
              customerName: reportResponse.data.data.customerInfo.name,
              accountNumber: reportResponse.data.data.accountAnalysis[0].accountNo.slice(-4),
              accType: reportResponse.data.data.accountAnalysis[0].accountType,
            });
          });

          await this.dataSource.transaction(async (manager) => {
            await manager.update(LoanApplication, application.id, {
              subStatus: ApplicationSubStatus.INCOME_VERIFICATION,
            });

            await manager.save(ApplicationEventLog, {
              applicationId: application.id,
              eventType: ApplicationSubStatus.INCOME_VERIFICATION,
            });
          });

          return {
            success: true,
            message: 'Income report fetched successfully',
            data: { retry: false },
          };
        }
      } else if (status === 'COMPLETED' && errorCode !== 'NONE') {
        return {
          success: false,
          error: 'Failed to fetch income report',
        };
      }

      if (attempt < maxRetries) {
        this.logger.log(
          `Status is'${success}', '${status}', '${errorCode} retrying in 5 seconds... `
        );
        await this.delay(2000);
      }
    }

    this.logger.debug('Three retries completed. Transaction not yet completed.');
    return {
      success: true,
      message: 'Transaction not yet completed, please retry',
      data: { retry: true },
    };
  }

  delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
