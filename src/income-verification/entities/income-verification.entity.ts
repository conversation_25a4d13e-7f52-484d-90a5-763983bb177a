import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index } from 'typeorm';

@Entity({ name: 'income_verification' })
export class IncomeVerification extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ name: 'application_id' })
  applicationId: number;

  @ManyToOne(() => LoanApplication, (loanApp) => loanApp.id)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Index()
  @Column({ type: 'varchar', length: 255 })
  txnId: string;

  @Column({ name: 'is_txn_completed', type: 'boolean', default: false })
  isTxnCompleted: boolean;

  @Column({ name: 'response', type: 'jsonb', nullable: true })
  response: Record<string, any>;
}
