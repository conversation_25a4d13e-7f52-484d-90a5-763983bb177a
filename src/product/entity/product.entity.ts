import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { PartnerProductMapping } from 'src/partner-product-mapping/entity/partner-product-mapping.entity';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';

@Entity({ name: 'products' })
export class Product extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_code', type: 'char', length: 2 })
  productCode: string;

  @Column({ name: 'product_name', type: 'varchar', length: 255 })
  productName: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @OneToMany(() => PartnerProductMapping, (mapping) => mapping.product)
  partnerProductMappings: PartnerProductMapping[];
}
