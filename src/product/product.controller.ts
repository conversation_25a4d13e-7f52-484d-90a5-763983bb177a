import { Controller, Get, UseGuards } from '@nestjs/common';
import { ProductService } from './product.service';
import { success_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  async getProducts() {
    const products = await this.productService.getAllProducts();

    return success_response(
      products,
      !products.length ? 'No products found' : 'Products fetched successfully'
    );
  }
}
