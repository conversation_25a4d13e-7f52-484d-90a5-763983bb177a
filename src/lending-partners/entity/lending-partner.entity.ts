import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { PartnerProductMapping } from 'src/partner-product-mapping/entity/partner-product-mapping.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

export enum EngagementType {
  DLG = 'dlg',
  COLENDING = 'colending',
}

@Entity('lending_partners')
export class LendingPartner extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: EngagementType,
    default: EngagementType.DLG,
  })
  engagementType: EngagementType;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @OneToMany(() => PartnerProductMapping, (mapping) => mapping.lendingPartner)
  partnerProductMappings: PartnerProductMapping[];

  @OneToMany(() => LoanApplication, (loan) => loan.lendingPartner)
  loanApplications: LoanApplication[];
}
