import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoanPurposeMaster } from './entity/loan-purpose-master.entity';
import { Message } from 'src/utils/response-util';

@Injectable()
export class LoanPurposeMasterService {
  constructor(
    @InjectRepository(LoanPurposeMaster)
    private readonly loanPurposeRepo: Repository<LoanPurposeMaster>
  ) {}

  async findAll(productId?: number): Promise<Message> {
    const where: Record<string, any> = { isActive: true };
    if (productId) where.productId = productId;

    const data = await this.loanPurposeRepo.find({
      where,
      order: { id: 'ASC' },
    });

    if (data.length === 0) {
      return {
        success: false,
        error: productId
          ? `No loan purposes found for productId: ${productId}`
          : 'No loan purposes found.',
      };
    }

    return {
      success: true,
      data,
    };
  }
}
