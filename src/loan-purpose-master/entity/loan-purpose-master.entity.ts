import { En<PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from 'src/product/entity/product.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity()
export class LoanPurposeMaster extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'product_id', type: 'int' })
  productId: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'text' })
  purpose: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;
}
