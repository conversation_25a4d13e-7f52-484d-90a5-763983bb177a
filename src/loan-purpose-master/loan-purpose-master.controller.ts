import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { LoanPurposeMasterService } from './loan-purpose-master.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('loan-purpose')
export class LoanPurposeMasterController {
  constructor(private readonly loanPurposeService: LoanPurposeMasterService) {}

  @Get()
  async getAll(@Query('productId') productId?: string) {
    return fixed_response(
      await this.loanPurposeService.findAll(productId ? Number(productId) : undefined)
    );
  }
}
