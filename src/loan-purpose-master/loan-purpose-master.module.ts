import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoanPurposeMasterController } from './loan-purpose-master.controller';
import { LoanPurposeMasterService } from './loan-purpose-master.service';
import { LoanPurposeMaster } from './entity/loan-purpose-master.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LoanPurposeMaster])],
  controllers: [LoanPurposeMasterController],
  providers: [LoanPurposeMasterService],
  exports: [LoanPurposeMasterService],
})
export class LoanPurposeMasterModule {}
