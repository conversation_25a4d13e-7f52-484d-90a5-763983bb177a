import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { dataSourceOptions } from './db/data-source';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { LoanApplicationModule } from './loan-application/loan-application.module';
import { ProductModule } from './product/product.module';
import { ServiceablePincodeModule } from './serviceable-pincode/serviceable-pincode.module';
import { AddressesModule } from './addresses/addresses.module';
import { LookupMasterModule } from './lookup-master/lookup-master.module';
import { LoanPurposeMasterModule } from './loan-purpose-master/loan-purpose-master.module';
import { ApplicantDetailsModule } from './applicant-details/applicant-details.module';
import { ClsModule } from 'nestjs-cls';
import { FrozenAccountModule } from './frozen-accounts/frozen-account.module';
import { LoanOfferModule } from './loan-offer/loan-offer.module';
import { UserConsentModule } from './user-consent/user-consent.module';
import { BureauModule } from './bureau/bureau.module';
import { Request } from 'express';
import { FileUploadModule } from './file-upload/file-upload.module';
import { IncomeVerificationModule } from './income-verification/income-verification.module';
import { PincodeMasterModule } from './pincode-master/pincode-master.module';
import { BankAccountModule } from './bank-account/bank-account.module';
import { LendingPartnersModule } from './lending-partners/lending-partners.module';
import { ApplicationEventLogModule } from './application-event-log/application-event-log.module';
import { ApplicationDocumentsModule } from './application-documents/application-documents.module';
import { VideoKycModule } from './video-kyc/video-kyc.module';
import { EncryptionModule } from './encryption/encryption.module';
import { MandateRegistrationModule } from './mandate-registration/mandate-registration.module';
import { DsaManagementModule } from './dsa-management/dsa-management.module';
import { BusinessDetailsModule } from './business-details/business-details.module';
import { ApplicationCpvModule } from './application-cpv/application-cpv.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
    }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        setup: (cls, req: Request) => {
          cls.set('header', req.headers);
        },
      },
    }),
    TypeOrmModule.forRoot({ ...dataSourceOptions, autoLoadEntities: true }),
    LoanApplicationModule,
    ProductModule,
    ServiceablePincodeModule,
    AddressesModule,
    LookupMasterModule,
    BureauModule,
    LoanPurposeMasterModule,
    ApplicantDetailsModule,
    FrozenAccountModule,
    LoanOfferModule,
    UserConsentModule,
    FileUploadModule,
    IncomeVerificationModule,
    PincodeMasterModule,
    BankAccountModule,
    LendingPartnersModule,
    ApplicationEventLogModule,
    ApplicationDocumentsModule,
    VideoKycModule,
    EncryptionModule,
    MandateRegistrationModule,
    DsaManagementModule,
    BusinessDetailsModule,
    ApplicationCpvModule,
  ],
  controllers: [AppController],
})
export class AppModule {
  constructor() {}
}
