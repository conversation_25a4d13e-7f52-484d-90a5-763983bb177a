import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsEnum,
  IsInt,
  ValidateIf,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { AccommodationType, AddressType } from '../entity/address.entity';
import { Type } from 'class-transformer';

export enum RelationWithOwner {
  SISTER = 'SISTER',
  BROTHER = 'BROTHER',
  MOTHER = 'MOTHER',
  FATHER = 'FATHER',
  SPOUSE = 'SPOUSE',
}

export class AttachmentDto {
  @IsString()
  type: string;

  @IsString()
  fileKey: string;
}

export class AddressMetadataDto {
  @IsOptional()
  @IsString()
  ownerName?: string;

  @IsOptional()
  @IsString()
  ownerMobile?: string;

  @IsOptional()
  @IsEnum(RelationWithOwner, { message: 'Invalid relation with owner' })
  relationWithOwner?: RelationWithOwner;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  attachments?: AttachmentDto[];
}

export class CreateAddressesDto {
  @IsNotEmpty()
  @IsInt()
  applicationId?: number;

  @ValidateIf((o: CreateAddressesDto) => !o.referenceAddressId)
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @IsOptional()
  @IsString()
  addressLine2?: string;

  @ValidateIf((o: CreateAddressesDto) => !o.referenceAddressId)
  @IsString()
  @IsNotEmpty()
  city: string;

  @ValidateIf((o: CreateAddressesDto) => !o.referenceAddressId)
  @IsString()
  @IsNotEmpty()
  state: string;

  @ValidateIf((o: CreateAddressesDto) => !o.referenceAddressId)
  @IsString()
  @IsNotEmpty()
  pincode: string;

  @IsEnum(AddressType)
  type: AddressType;

  @IsOptional()
  @IsInt()
  referenceAddressId?: number;

  @IsOptional()
  @IsEnum(AccommodationType, { message: 'Invalid accommodation type' })
  accommodationType?: AccommodationType;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressMetadataDto)
  metadata?: AddressMetadataDto;
}

class ElectricityDto {
  @IsNotEmpty()
  @IsString()
  consumerId: string;

  @IsNotEmpty()
  @IsString()
  serviceProvider: string;

  @IsOptional()
  @IsString()
  district: string;
}

class AddressDto {
  @IsNotEmpty()
  @IsString()
  propertyId: string;

  @IsNotEmpty()
  @IsString()
  city: string;
}

export class AddressVerificationDto {
  @IsNotEmpty()
  @IsInt()
  applicationId: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => ElectricityDto)
  electricity?: ElectricityDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;
}
