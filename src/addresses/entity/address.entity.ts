import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

export enum AddressType {
  RESIDENTIAL = 'RESIDENTIAL',
  OFFICE = 'OFFICE',
  CURRENT = 'CURRENT',
  PERMANENT = 'PERMANENT',
}

export enum AccommodationType {
  SELF_OWNED = 'SELF_OWNED',
  FAMILY_OWNED = 'FAMILY_OWNED',
  PG = 'PG',
  RENTAL_ALONE = 'RENTAL_ALONE',
  RENTAL_FAMILY = 'RENTAL_FAMILY',
  COMPANY_ALLOTTED = 'COMPANY_ALLOTTED',
}

@Entity({ name: 'addresses' })
@Index('idx_address_entity_lookup', ['entityType', 'entityId', 'type'])
export class Address extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'addressline1', type: 'varchar' })
  addressLine1: string;

  @Column({ name: 'addressline2', type: 'varchar', nullable: true })
  addressLine2?: string;

  @Column({ name: 'pincode', type: 'varchar', length: 6 })
  pincode: string;

  @Column({ name: 'city', type: 'varchar' })
  city: string;

  @Column({ name: 'state', type: 'varchar' })
  state: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: AddressType,
  })
  type: AddressType;

  @Column({ name: 'entity_type', type: 'varchar' })
  entityType: string;

  @Column({ name: 'entity_id', type: 'int' })
  entityId: number;

  @Column({ name: 'is_verified', type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({
    name: 'accommodation_type',
    type: 'enum',
    enum: AccommodationType,
    nullable: true,
  })
  accommodationType?: AccommodationType;

  @Column({ name: 'source', type: 'varchar', nullable: true })
  source?: string;

  @Column({ name: 'linked_entity_type', type: 'varchar', nullable: true })
  linkedEntityType: string;

  @Column({ name: 'linked_entity_id', type: 'int', nullable: true })
  linkedEntityId: number;
}
