import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { AddressVerificationDto, CreateAddressesDto } from './dto/addresses.dto';
import { fixed_response } from 'src/utils/response-util';
import { AddressesService } from './addresses.service';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('addresses')
export class AddressesController {
  constructor(private readonly addressesService: AddressesService) {}

  @Post()
  async create(@Body() dto: CreateAddressesDto) {
    return fixed_response(await this.addressesService.createAddress(dto));
  }

  @Put(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() dto: Partial<CreateAddressesDto>) {
    return fixed_response(await this.addressesService.updateAddress(id, dto));
  }

  @Delete(':id')
  async delete(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.addressesService.deleteAddress(id));
  }

  @Get('my')
  async getMyAddresses() {
    return fixed_response(await this.addressesService.getMyAddresses());
  }

  @Get(':id')
  async getById(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.addressesService.getAddressById(id));
  }

  @Post('verify/:id')
  async verifyAddress(@Param('id', ParseIntPipe) id: number, @Body() dto: AddressVerificationDto) {
    return fixed_response(await this.addressesService.verifyCurrentAddress(id, dto));
  }
}
