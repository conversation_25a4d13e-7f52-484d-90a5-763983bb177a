import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressesService } from './addresses.service';
import { AddressesController } from './addresses.controller';
import { Address } from './entity/address.entity';
import { HttpModule } from '@nestjs/axios';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Address, ApplicantDetail]), HttpModule],
  controllers: [AddressesController],
  providers: [AddressesService],
  exports: [AddressesService],
})
export class AddressesModule {}
