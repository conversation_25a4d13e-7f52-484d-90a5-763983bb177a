import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, IsNull, Not, Repository } from 'typeorm';
import { Message } from 'src/utils/response-util';
import { AccommodationType, Address, AddressType } from 'src/addresses/entity/address.entity';
import { CreateAddressesDto, AddressVerificationDto } from 'src/addresses/dto/addresses.dto';
import { VerificationItem } from 'src/utils/address-verification.util';
import { getVerificationMetadata } from '../utils/address-verification.util';
import { ClsService } from 'nestjs-cls';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { instanceToPlain } from 'class-transformer';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';

@Injectable()
export class AddressesService {
  private readonly logger = new Logger(AddressesService.name);

  constructor(
    @InjectRepository(Address)
    private readonly addressRepo: Repository<Address>,
    private readonly cls: ClsService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(ApplicantDetail)
    private readonly applicantRepo: Repository<ApplicantDetail>,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async createAddress(dto: CreateAddressesDto): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');

      const applicant = await this.applicantRepo
        .createQueryBuilder('applicant')
        .innerJoinAndSelect('applicant.application', 'application')
        .where('application.id = :applicationId', { applicationId: dto.applicationId })
        .andWhere('application.userId = :userId', { userId })
        .getOne();

      if (!applicant) {
        return { success: false, error: 'Unauthorised access or invalid application' };
      }

      if (dto.type === AddressType.CURRENT && dto.referenceAddressId) {
        const refAddress = await this.addressRepo.findOne({
          where: {
            id: dto.referenceAddressId,
            entityType: 'user',
            entityId: userId,
          },
        });

        if (!refAddress) {
          return {
            success: false,
            error: 'Reference address not found',
          };
        }

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, createdAt, updatedAt, deletedAt, ...copiedFields } = refAddress;

        const newAddress = this.addressRepo.create({
          ...copiedFields,
          type: AddressType.CURRENT,
          linkedEntityType: 'applicant_details',
          linkedEntityId: applicant.id,
        });

        const saved = await this.addressRepo.save(newAddress);

        return {
          success: true,
          data: {
            message: 'Address cloned successfully',
            id: saved.id,
          },
        };
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { applicationId, ...rest } = dto;

      const address = this.addressRepo.create({
        ...rest,
        entityType: 'user',
        entityId: userId,
        linkedEntityType: 'applicant_details',
        linkedEntityId: applicant.id,
      });
      const saved = await this.addressRepo.save(address);

      return {
        success: true,
        data: {
          message: 'Address created successfully',
          id: saved.id,
        },
      };
    } catch (error) {
      this.logger.error('Create Address Error:', error);
      return {
        success: false,
        error: 'Failed to create address',
      };
    }
  }

  async updateAddress(id: number, dto: Partial<CreateAddressesDto>): Promise<Message> {
    const address = await this.addressRepo.findOne({
      where: {
        id,
        entityType: 'user',
        entityId: this.cls.get<number>('userId'),
        linkedEntityType: Not(IsNull()),
      },
    });
    if (!address) {
      return {
        success: false,
        error: 'Address not found or unauthorized access',
      };
    }

    this.logger.debug('address>>', address);

    try {
      const updateData: any = {
        ...dto,
        ...(dto.metadata && { metadata: instanceToPlain(dto.metadata) }),
      };

      await this.addressRepo.update(id, updateData);

      const responseData: Record<string, any> = {
        id,
        message: 'Address updated successfully',
      };

      if (dto.accommodationType == AccommodationType.COMPANY_ALLOTTED) {
        const applicant = await this.applicantRepo.findOne({
          where: { id: address.linkedEntityId },
        });
        await this.dataSource.transaction(async (manager) => {
          await manager.update(Address, address.id, { isVerified: true });
          await manager.update(LoanApplication, applicant!.applicationId, {
            subStatus: ApplicationSubStatus.ADDRESS_VERIFICATION,
          });

          await manager.save(ApplicationEventLog, {
            applicationId: applicant!.applicationId,
            eventType: ApplicationSubStatus.ADDRESS_VERIFICATION,
          });
        });

        return {
          success: true,
          data: {
            isVerified: true,
          },
          message: 'Verification successful and address updated',
        };
      } else {
        const verification: VerificationItem[] = getVerificationMetadata(
          dto.accommodationType,
          address.state
        );

        responseData.verification = verification;
      }

      return {
        success: true,
        data: responseData,
      };
    } catch (error) {
      this.logger.error('Update Address Error:', error);
      return {
        success: false,
        error: 'Failed to update address',
      };
    }
  }

  async deleteAddress(id: number): Promise<Message> {
    const address = await this.addressRepo.findOne({
      where: { id, entityType: 'user', entityId: this.cls.get<number>('userId') },
    });
    if (!address) {
      return {
        success: false,
        error: 'Address not found or unauthorized access',
      };
    }

    try {
      await this.addressRepo.softDelete(id);
      return {
        success: true,
        data: {},
        message: 'Address deleted successfully',
      };
    } catch (error) {
      this.logger.error('Delete Address Error:', error);
      return {
        success: false,
        error: 'Failed to delete address',
      };
    }
  }

  async getAddressById(id: number): Promise<Message> {
    try {
      const address = await this.addressRepo.findOne({
        where: { id, entityType: 'user', entityId: this.cls.get<number>('userId') },
      });
      if (!address) {
        return {
          success: false,
          error: 'Address not found.',
        };
      }
      return {
        success: true,
        data: address,
      };
    } catch (error) {
      this.logger.error('Get Address Error:', error);
      return {
        success: false,
        error: 'Failed to retrieve address',
      };
    }
  }

  async getMyAddresses(): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const existingPermanent = await this.addressRepo.findOne({
        where: {
          entityType: 'user',
          entityId: userId,
          type: AddressType.PERMANENT,
        },
        order: { createdAt: 'DESC' },
      });

      if (!existingPermanent) {
        const baseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');
        try {
          const response = await firstValueFrom(
            this.httpService.get(`${baseUrl}/kyc/api/aadhaar-address`, {
              headers: {
                Authorization: this.cls.get('header')?.['authorization'],
              },
            })
          );

          const aadhaarAddress = response?.data?.data;
          if (aadhaarAddress) {
            const { addressline1, addressline2, pincode, city, state } = aadhaarAddress.address;

            await this.addressRepo.save({
              entityId: userId,
              entityType: 'user',
              type: AddressType.PERMANENT,
              addressLine1: addressline1,
              addressLine2: addressline2,
              pincode,
              city,
              state,
              isVerified: true,
            });
          }
        } catch (apiError) {
          this.logger.error('Failed to fetch Aadhaar address:', apiError);
          return {
            success: false,
            error: 'Unable to fetch address from Aadhaar service',
          };
        }
      }

      const addresses = await this.addressRepo.find({
        where: {
          entityType: 'user',
          entityId: userId,
        },
        order: { createdAt: 'DESC' },
      });

      return {
        success: true,
        data: {
          addresses,
        },
        message: 'User addresses fetched successfully',
      };
    } catch (error) {
      this.logger.error('Get Addresses Error:', error);
      return {
        success: false,
        error: 'Failed to fetch addresses',
      };
    }
  }

  async verifyCurrentAddress(id: number, dto: AddressVerificationDto): Promise<Message> {
    const address = await this.addressRepo.findOne({
      where: { id, entityType: 'user', entityId: this.cls.get<number>('userId') },
    });

    if (!address || address.type !== AddressType.CURRENT) {
      return { success: false, error: 'Address not found' };
    }

    let referenceName;
    const applicant = await this.applicantRepo.findOne({
      where: { id: address.linkedEntityId, applicationId: dto.applicationId },
    });

    if (!applicant) {
      return { success: false, error: 'Applicant not found for the address' };
    }

    if (address.accommodationType === AccommodationType.SELF_OWNED) {
      referenceName = applicant.name.trim();
    } else {
      referenceName = address.metadata?.ownerName?.trim();
    }

    const { endpoint, payload } = this.buildPayloadAndEndpoint(dto, address);
    const rawPayload = { ...payload };

    if (
      endpoint === 'electricity-verification' &&
      address.state?.toLowerCase() === 'tamil nadu' &&
      rawPayload.consumerId
    ) {
      rawPayload.consumerId = rawPayload.consumerId.slice(2).trim();
    }

    const baseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL');

    try {
      let matched = false;
      const isTestEnv = this.configService.get<string>('APP_ENV') !== 'production';
      if (isTestEnv) {
        if (rawPayload.consumerId === '1234567890' || rawPayload.propertyId === '1234567890') {
          matched = true;
        }
      } else {
        const response = await firstValueFrom(
          this.httpService.post(`${baseUrl}/kyc/api/${endpoint}`, rawPayload, {
            headers: { Authorization: this.cls.get('header')?.['authorization'] },
          })
        );

        const apiData = response.data;
        if (!apiData?.success) {
          return { success: false, error: 'External verification failed' };
        }

        const data = apiData.data;

        if (endpoint === 'electricity-verification') {
          matched = data.consumer_name?.trim() === referenceName;
        } else if (endpoint === 'property-tax') {
          const propertyName = data?.ownerDetails?.[0]?.ownerName?.trim();
          matched = propertyName === referenceName;
        }
      }

      if (matched) {
        await this.dataSource.transaction(async (manager) => {
          await manager.update(Address, address.id, { isVerified: true });
          await manager.update(LoanApplication, dto.applicationId, {
            subStatus: ApplicationSubStatus.ADDRESS_VERIFICATION,
          });

          await manager.save(ApplicationEventLog, {
            applicationId: dto.applicationId,
            eventType: ApplicationSubStatus.ADDRESS_VERIFICATION,
          });
        });

        return {
          success: true,
          data: {
            isVerified: true,
          },
          message: 'Verification successful and address updated',
        };
      }
      await this.dataSource.transaction(async (manager) => {
        await manager.update(LoanApplication, +dto.applicationId, {
          subStatus: ApplicationSubStatus.ADDRESS_VERIFICATION_FAILED,
          statusId: 7,
        });
        const frozenTillDate: Date = new Date();
        frozenTillDate.setDate(frozenTillDate.getDate() + 90);
        await manager.save(FrozenAccount, {
          pan: applicant.pan,
          userId: address.entityId,
          frozenTillDate,
          reason: `Application #${dto.applicationId} Rejected`,
        });
      });

      return {
        success: false,
        error: 'We regret to inform you that the application cannot be accepted at the moment.',
      };
    } catch (err) {
      const message = err?.response?.data?.message || err.message || 'Verification failed';
      this.logger.error(`Verification API failed: ${endpoint} ${message}`);
      return {
        success: false,
        error: message,
      };
    }
  }

  removeUndefined(obj: Record<string, any>): Record<string, any> {
    return Object.fromEntries(Object.entries(obj).filter(([_, value]) => value !== undefined));
  }

  /**
   * Determines the verification endpoint and payload based on dto content.
   */

  private buildPayloadAndEndpoint(
    dto: AddressVerificationDto,
    address: any
  ): {
    endpoint: string;
    payload: Record<string, any> | null;
  } {
    if (dto.electricity) {
      return {
        endpoint: 'electricity-verification',
        payload: this.removeUndefined(dto.electricity),
      };
    }

    if (dto.address) {
      const payload = {
        ...this.removeUndefined(dto.address),
        state: address.state,
      };

      return {
        endpoint: 'property-tax',
        payload,
      };
    }

    return { endpoint: '', payload: null };
  }
}
