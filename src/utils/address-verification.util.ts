import { AccommodationType } from 'src/addresses/entity/address.entity';

export type VerificationItem = { type: string; values?: string[] };

export function getVerificationMetadata(
  accommodationType?: string | AccommodationType,
  state?: string
): VerificationItem[] {
  const allowedTypes = [
    AccommodationType.SELF_OWNED,
    AccommodationType.FAMILY_OWNED,
    AccommodationType.RENTAL_ALONE,
    AccommodationType.RENTAL_FAMILY,
    AccommodationType.PG,
  ];

  const eligibleStates = ['ANDHRA PRADESH', 'DELHI', 'MAHARASHTRA', 'TELANGANA', 'RAJASTHAN'];

  const result: VerificationItem[] = [];

  if (accommodationType && allowedTypes.includes(accommodationType as AccommodationType)) {
    result.push({ type: 'electricity' });

    if (state && eligibleStates.includes(state.toUpperCase())) {
      const propertyValues = getPropertyVerification(state);
      result.push(...propertyValues);
    }
  }

  return result;
}

export function getPropertyVerification(state?: string): { type: string; values: string[] }[] {
  const stateCityMap: Record<string, string[]> = {
    MAHARASHTRA: ['Navi Mumbai', 'Greater Mumbai', 'Mira Bhayendar', 'Thane', 'Nagpur'],
    RAJASTHAN: ['Jaipur', 'Kota', 'Ajmer', 'Jodhpur', 'Bikaner'],
    'ANDHRA PRADESH': ['All Cities (Urban)'],
    DELHI: ['North Regions', 'South Regions', 'East Regions'],
    TELANGANA: ['Hyderabad', 'Remaining Cities (Urban)'],
  };

  if (state && state.toUpperCase() in stateCityMap) {
    return [{ type: 'property', values: stateCityMap[state.toUpperCase()] }];
  }

  return [];
}
