export function success_response(data: any, message: string = '') {
  return {
    success: true,
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    data,
    message,
    timestamp: new Date().toISOString(),
  };
}

export function failure_response(error: string) {
  return {
    success: false,
    error,
    timestamp: new Date().toISOString(),
  };
}

export interface Message {
  success: boolean;
  data?: any;
  message?: string;
  error?: string;
  total?: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

export function fixed_response(res: Message) {
  return { ...res, timestamp: new Date().toISOString() };
}

export function parseQuery(queryString: string): Record<string, any> {
  const filters: Record<string, any> = {};
  const expressions = queryString.split('and');

  for (const expr of expressions) {
    const [key, value] = expr.trim().split('=');
    if (key && value) {
      filters[key.trim()] = value.trim();
    }
  }

  return filters;
}
