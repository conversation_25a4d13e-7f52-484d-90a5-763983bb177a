import { Injectable } from '@nestjs/common';
import { EncryptedData } from './entity/encrypted-data.entity';
import { Repository } from 'typeorm';
import { promisify } from 'util';
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class EncryptionService {
  private readonly password: string;

  constructor(
    @InjectRepository(EncryptedData)
    private readonly encryptedDataRepo: Repository<EncryptedData>,
    private readonly configService: ConfigService
  ) {
    this.password = this.configService.getOrThrow<string>('ENCRYPTION_KEY');
  }

  async encryptAndStore(plainText: string): Promise<EncryptedData> {
    const { encryptedData, iv } = await this.encrypt(plainText);

    const encryptedDataRec = new EncryptedData();
    encryptedDataRec.encryptedValue = encryptedData;
    encryptedDataRec.iv = iv;

    return this.encryptedDataRepo.save(encryptedDataRec);
  }

  async decrypt(encryptedData: EncryptedData): Promise<string> {
    return await this.decryptData(this.password, encryptedData.encryptedValue, encryptedData.iv);
  }

  private async encrypt(text: string): Promise<{ encryptedData: string; iv: string }> {
    return await this.encryptData(this.password, text);
  }

  private async getKey(password: string) {
    return (await promisify(scrypt)(password, 'salt', 32)) as Buffer;
  }

  async encryptData(
    password: string,
    plainText: string
  ): Promise<{ encryptedData: string; iv: string }> {
    const iv = randomBytes(16);

    const cipher = createCipheriv('aes-256-ctr', await this.getKey(password), iv);
    const encrypted = Buffer.concat([cipher.update(plainText), cipher.final()]);
    return {
      encryptedData: encrypted.toString('hex'),
      iv: iv.toString('hex'),
    };
  }

  async decryptData(password: string, encryptedData: string, ivHex: string): Promise<string> {
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = createDecipheriv('aes-256-ctr', await this.getKey(password), iv);
    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(encryptedData, 'hex')),
      decipher.final(),
    ]);
    return decrypted.toString();
  }
}
