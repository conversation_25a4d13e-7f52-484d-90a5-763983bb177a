import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity({ name: 'pincode_master' })
export class PincodeMaster {
  @PrimaryColumn({ name: 'pincode', type: 'varchar', length: 6 })
  pincode: string;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ name: 'state', type: 'varchar', length: 100 })
  state: string;

  @Column({ name: 'city', type: 'varchar', length: 100 })
  city: string;
}
