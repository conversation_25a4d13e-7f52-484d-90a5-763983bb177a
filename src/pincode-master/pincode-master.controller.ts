import { Controller, Get, Query } from '@nestjs/common';
import { PincodeMasterService } from './pincode-master.service';
import { fixed_response } from 'src/utils/response-util';

@Controller('pincode-master')
export class PincodeMasterController {
  constructor(private readonly pincodeMasterService: PincodeMasterService) {}

  @Get()
  async getPincodeData(@Query('pincode') pincode: string) {
    return fixed_response(await this.pincodeMasterService.getPincodeData(pincode));
  }
}
