import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Message } from 'src/utils/response-util';
import { PincodeMaster } from './entity/pincode-master.entity';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class PincodeMasterService {
  constructor(
    @InjectRepository(PincodeMaster)
    private readonly pincodeMasterRepo: Repository<PincodeMaster>,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {}

  async getPincodeData(pincode: string): Promise<Message> {
    return await this.checkPincode(pincode);
  }

  async checkPincode(pincode: string) {
    let pincodeData = await this.pincodeMasterRepo.findOneBy({ pincode });

    if (!pincodeData) {
      const apiBaseUrl = this.configService.getOrThrow<string>('PINLOOKUP_BASE_URL');
      const apiUrl = `${apiBaseUrl}?pincode=${pincode}`;

      try {
        interface PinInfo {
          data: { taluk?: string; district_name: string; state_name: string };
        }
        const res = await firstValueFrom(this.httpService.get<PinInfo>(apiUrl));
        const pinInfo = res.data.data;

        if (!pinInfo) {
          return {
            success: false,
            error: 'No data found',
          };
        }

        const finalCity =
          pinInfo.taluk?.toUpperCase() === 'NA' ? pinInfo.district_name : pinInfo.taluk;

        pincodeData = this.pincodeMasterRepo.create({
          pincode: pincode,
          state: pinInfo.state_name,
          city: finalCity,
          metadata: pinInfo,
        });

        await this.pincodeMasterRepo.save(pincodeData);
      } catch (err) {
        return {
          success: false,
          error: 'External API failed',
        };
      }
    }

    return {
      success: true,
      message: 'Pincode details fetched successfully',
      data: {
        state: pincodeData.state,
        city: pincodeData.city,
      },
    };
  }
}
