import { Test, TestingModule } from '@nestjs/testing';
import { PincodeMasterController } from './pincode-master.controller';

describe('PincodeMasterController', () => {
  let controller: PincodeMasterController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PincodeMasterController],
    }).compile();

    controller = module.get<PincodeMasterController>(PincodeMasterController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
