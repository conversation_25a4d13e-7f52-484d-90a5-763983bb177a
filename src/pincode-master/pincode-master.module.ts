import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PincodeMaster } from './entity/pincode-master.entity';
import { PincodeMasterController } from './pincode-master.controller';
import { PincodeMasterService } from './pincode-master.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [TypeOrmModule.forFeature([PincodeMaster]), HttpModule],
  controllers: [PincodeMasterController],
  providers: [PincodeMasterService],
  exports: [PincodeMasterService],
})
export class PincodeMasterModule {}
