import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Message } from 'src/utils/response-util';

export interface QuestionsResponse extends Message {
  data: QuestionLog;
}

export interface QuestionLog {
  IVStatus?: string;
  ChallengeConfigGUID?: string;
  question?: Question | Question[];
  queueName?: string;
  Failure?: { FailureEnum: string; Message: string };
  questionId?: number;
  applicationId?: number;
}
export interface Question {
  AnswerChoice: {
    Key: string;
    AnswerChoiceId: string;
  };
  resendEligible: boolean;
  LastChanceQuestion: boolean;
  skipEligible: boolean;
  FullQuestionText: string;
  Key: string;
}

//Answer
export class Answer {
  @IsString()
  questionKey: string;

  @IsString()
  answerKey: string;

  @IsString()
  userInputAnswer: string;

  @IsOptional()
  @IsBoolean()
  resendOTP: boolean;

  @IsOptional()
  @IsBoolean()
  skipQuestion: boolean;
}

// Answer Verification
export class AnswerDto {
  @IsNumber()
  questionId?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Answer)
  answers?: Answer[];

  @IsNotEmpty()
  applicationId: number;
}
