import { Message } from 'src/utils/response-util';
import { QuestionsResponse } from './questions-response.dto';

export interface EnrollmentResponse extends Message {
  success: boolean;
  message?: string;
  error?: string;
  data?:
    | {
        question: QuestionsResponse | [];
        queueName: string;
      }
    | {
        enrollment: {
          partnerCustomerId: string;
        };
      };
}
