import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { LoanApplication } from '../../loan-application/entity/loan-application.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { UserToPartnerCustomer } from 'src/common/entity/user-to-partner-customer.entity';

@Entity({ name: 'cibil_question_log' })
export class CibilQuestionLog extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => LoanApplication)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Index() // column index for FK
  @Column({ name: 'application_id' })
  applicationId: number;

  @Column({ name: 'pcc_id', type: 'uuid' })
  pccId: string;

  // Add relationship to UserToPartnerCustomer
  @ManyToOne(() => UserToPartnerCustomer)
  @JoinColumn({ name: 'pcc_id', referencedColumnName: 'partnerCustomerId' })
  userMapping: UserToPartnerCustomer;

  @Column({ type: 'jsonb' })
  question: Record<string, any>;

  @Index()
  @Column({ name: 'challenge_guid', length: 50 })
  challengeGuid: string;
}
