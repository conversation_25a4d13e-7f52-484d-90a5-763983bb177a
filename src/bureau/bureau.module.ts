import { Modu<PERSON> } from '@nestjs/common';
import { BureauController } from './bureau.controller';
import { BureauService } from './bureau.service';
import { TypeOrmModule } from '@nestjs/typeorm/dist/typeorm.module';
import { Address } from 'src/addresses/entity/address.entity';
import { UserToPartnerCustomer } from 'src/common/entity/user-to-partner-customer.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { HttpModule } from '@nestjs/axios';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import { CibilService } from './cibil.service';
import { CibilQuestionLog } from './entity/cibil-question-log.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Address,
      UserToPartnerCustomer,
      LoanApplication,
      ApplicantDetail,
      CibilQuestionLog,
    ]),
    HttpModule,
  ],
  controllers: [BureauController],
  providers: [BureauService, CibilService],
})
export class BureauModule {}
