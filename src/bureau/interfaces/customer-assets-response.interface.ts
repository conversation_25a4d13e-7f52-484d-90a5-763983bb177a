import { Message } from 'src/utils/response-util';

export interface CustomerAssetsResponse extends Message {
  creditEnquiries?: number | null;
  creditScore?: number | null;
  creditScoreName?: string | null;
  creditScoreFactors?: string[] | null;

  // Custom Month Metrics
  newAccountsCount?: number | null;
  newAccountsMonths?: number;
  newLoanAccountsCount?: number | null;
  newDelinquentAccountsCount?: number | null;
  newDelinquentAccountsMonths?: number;
  newEnquiriesCount?: number | null;
  newEnquiriesMonths?: number;
  dpdAccounts90PlusCount?: number | null;
  dpdAccounts90PlusMonths?: number;
  suitFiledCount?: number | null;
  suitFiledMonths?: number;
  wilfulDefaultCount?: number | null;
  wilfulDefaultMonths?: number;
  settledAccountCount?: number | null;
  settledAccountMonths?: number;

  limitUtilization?: number | null;
  totalPaymentsReported?: number | null;
  paymentOnTimeRatio?: number | null;

  lossAccount?: number | null;
}

export interface CreditAnalysis {
  creditEnquiries?: number | null;
  creditScore?: number | null;
  creditScoreName?: string | null;
  creditScoreFactors?: string[] | null;

  // Custom Month Metrics
  newAccountsCount?: number | null;
  newAccountsMonths?: number;
  newLoanAccountsCount?: number | null;
  newDelinquentAccountsCount?: number | null;
  newDelinquentAccountsMonths?: number;
  newEnquiriesCount?: number | null;
  newEnquiriesMonths?: number;
  dpdAccounts90PlusCount?: number | null;
  dpdAccounts90PlusMonths?: number;
  suitFiledCount?: number | null;
  suitFiledMonths?: number;
  wilfulDefaultCount?: number | null;
  wilfulDefaultMonths?: number;
  settledAccountCount?: number | null;
  settledAccountMonths?: number;

  limitUtilization?: number | null;
  totalPaymentsReported?: number | null;
  paymentOnTimeRatio?: number | null;

  lossAccount?: number | null;
}
