import { Injectable } from '@nestjs/common';
import { AnswerDto } from './dto/questions-response.dto';
import { VerificationResponse } from './interfaces/verification-response.interface';
import { Message } from 'src/utils/response-util';
import { CibilService } from './cibil.service';

@Injectable()
export class BureauService {
  constructor(private readonly cibilService: CibilService) {}

  async enrollCustomer(applicationId: number): Promise<Message> {
    return this.cibilService.enrollCustomer(applicationId);
  }

  async verifyAnswers(answer: AnswerDto): Promise<VerificationResponse> {
    return this.cibilService.verifyAnswers(answer);
  }
}
