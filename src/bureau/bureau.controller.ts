import { Body, Controller, Post, Get, UseGuards, Query, ParseIntPipe } from '@nestjs/common';
import { BureauService } from './bureau.service';
import { AnswerDto } from './dto/questions-response.dto';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { Message } from 'src/utils/response-util';

@UseGuards(DecodeTokenGuard)
@Controller('bureau')
export class BureauController {
  constructor(private readonly bureauService: BureauService) {}

  @Get('initiate-bureau-check')
  async enrollCustomer(
    @Query('applicationId', ParseIntPipe) applicationId: number
  ): Promise<Message> {
    return this.bureauService.enrollCustomer(applicationId);
  }

  @Post('fetch-bureau-data')
  async verifyAnswers(@Body() dto: AnswerDto): Promise<Message> {
    return this.bureauService.verifyAnswers(dto);
  }
}
