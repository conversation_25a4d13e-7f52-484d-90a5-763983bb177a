import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AnswerDto, QuestionsResponse } from './dto/questions-response.dto';
import { VerificationResponse } from './interfaces/verification-response.interface';
import { CustomerAssetsResponse } from './interfaces/customer-assets-response.interface';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Address, AddressType } from 'src/addresses/entity/address.entity';
import { ClsService } from 'nestjs-cls';
import { Repository } from 'typeorm/repository/Repository';
import { UserToPartnerCustomer } from '../common/entity/user-to-partner-customer.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import { Message } from 'src/utils/response-util';
import { StateCode } from 'src/common/enums/dtc-state.enum';
import { CibilQuestionLog } from './entity/cibil-question-log.entity';
import { v4 as uuid } from 'uuid';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class CibilService {
  private readonly baseUrl: string;
  private readonly logger = new Logger(CibilService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    @InjectRepository(UserToPartnerCustomer)
    private readonly userMappingRepository: Repository<UserToPartnerCustomer>,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepository: Repository<LoanApplication>,
    @InjectRepository(ApplicantDetail)
    private readonly applicantDetailRepository: Repository<ApplicantDetail>,
    private readonly cls: ClsService,
    @InjectRepository(CibilQuestionLog)
    private readonly cibilQuestionLogRepo: Repository<CibilQuestionLog>,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    this.baseUrl = this.configService.getOrThrow<string>('AUTH_SERVICE_BASE_URL') + '/kyc/api';
  }

  // Customer Enrollment with automatic questions retrieval
  async enrollCustomer(applicationId: number): Promise<Message> {
    const userId = this.cls.get<number>('userId');

    // Fetch latest loan application for the user
    const loanApplication = await this.loanApplicationRepository.findOne({
      where: {
        id: applicationId,
        userId,
        statusId: 1,
        subStatus: ApplicationSubStatus.ADDRESS_VERIFICATION,
      },
    });

    if (!loanApplication) {
      throw new Error('No loan application found for user');
    }

    try {
      // Check for existing mapping
      const existingMapping = await this.userMappingRepository.findOne({ where: { userId } });
      if (existingMapping) {
        if (existingMapping.ivStatus === 'Success') {
          return { success: true, data: { question: [], queueName: 'FETCH_DIRECTLY' } };
        }
        return this.getAuthenticationQuestions(
          existingMapping.partnerCustomerId,
          loanApplication.id
        );
      }

      // Fetch applicant details using the loan application ID
      const applicantDetails = await this.applicantDetailRepository.findOne({
        where: { applicationId: loanApplication.id },
      });

      if (!applicantDetails) {
        throw new Error('Applicant details not found');
      }

      // Get primary address
      const primaryAddress = await this.addressRepository.findOne({
        where: { entityId: userId, entityType: 'user', type: AddressType.PERMANENT },
      });

      if (!primaryAddress) {
        throw new Error('No residential address found');
      }

      // Get region ID from DTC state code mapping
      const regionId = this.getRegionIdFromState(primaryAddress.state);

      // Split name with special handling for single-word names
      const { forename, surname } = this.getSeparatedUserName(applicantDetails.name);

      // Use PAN from applicant details for IdentificationId
      const identificationId = applicantDetails.pan.toUpperCase().replace(/\s+/g, '');

      // Remove special characters from address line 1
      const sanitizedAddress = primaryAddress.addressLine1
        .replace(/[<>"'&]/g, '') // Remove < > " ' & characters
        .replace(/\s{2,}/g, ' ') // Replace multiple spaces with single space
        .trim();

      // Generate a new partner_customer_id (but don't save it yet)
      const newPartnerCustomerId = uuid();

      // Prepare enrollment payload
      const enrollmentPayload = {
        partnerCustomerId: newPartnerCustomerId, // Use generated ID
        email: applicantDetails.email,
        phoneNumber: applicantDetails.mobile,
        forename,
        surname,
        identificationId,
        streetAddress: sanitizedAddress,
        city: primaryAddress.city,
        postalCode: primaryAddress.pincode,
        region: regionId,
        dateOfBirth: applicantDetails.dob.split('T')[0], // Format as YYYY-MM-DD
        gender: applicantDetails.gender === 'M' ? 'Male' : 'Female',
      };

      // Step 1: Customer enrollment
      const enrollment = (await this.postToCibil('customer-enrollment', enrollmentPayload)) as {
        success: boolean;
        data: {
          Status: string;
          Failure: { FailureEnum: string; Message: string; ClientUserKey?: string };
        };
      };

      this.logger.debug('Enrollment response :', enrollment);

      if (!enrollment.success) {
        return {
          success: false,
          error: 'Enrollment failed',
        };
      }

      // Handle enrollment status
      let result;
      if (enrollment.data.Status) {
        result = await this.handleCibilEnrollmentStatus(
          enrollment.data.Status,
          newPartnerCustomerId, // Use the generated ID
          loanApplication.id
        );
      } else if (enrollment.data.Failure) {
        result = this.handleCibilFailure(enrollment.data.Failure, loanApplication.id);
      } else {
        result = { success: false, error: 'Unexpected response from CIBIL enrollment' };
      }

      return result;
    } catch (err) {
      this.logger.error('Enrollment error:', err);
      throw new Error(err);
    }
  }

  private async handleCibilEnrollmentStatus(
    status: string,
    partnerCustomerId: string,
    applicationId: number
  ): Promise<Message> {
    switch (status) {
      case 'Success':
        return {
          success: true,
          data: {
            question: [],
            queueName: 'FETCH_DIRECTLY',
          },
        };
      case 'InProgress':
      case 'Pending': {
        // Step 2: Get authentication questions
        await this.savePartnerCustomerMapping(this.cls.get<number>('userId'), partnerCustomerId);
        return this.getAuthenticationQuestions(partnerCustomerId, applicationId);
      }
      case 'Failure':
        return { success: false, error: 'Enrollment failed. Restart CIBIL verification' };
      default:
        return {
          success: false,
          error: 'Unknown enrollment status. Restart CIBIL verification',
        };
    }
  }

  private async handleCibilFailure(
    failure: {
      FailureEnum: string;
      Message: string;
      ClientUserKey?: string;
    },
    loanApplicationId: number
  ): Promise<Message> {
    switch (failure.FailureEnum) {
      case 'NO_HIT':
        return { success: true, data: 'NO_HIT' };
      case 'BLACKLISTED_CUSTOMER':
        return { success: false, error: 'Customer is blacklisted' };
      case 'SSN_EXISTS': {
        if (failure.ClientUserKey) {
          try {
            // Check if mapping exists
            const userId = this.cls.get<number>('userId');
            let mapping = await this.userMappingRepository.findOne({
              where: { partnerCustomerId: failure.ClientUserKey, userId },
            });

            if (mapping)
              return {
                success: false,
                error: 'Same SSN exists for different user. Pls contact admin.',
              };

            mapping = new UserToPartnerCustomer();
            mapping.userId = userId;
            mapping.partnerCustomerId = failure.ClientUserKey;
            await this.userMappingRepository.save(mapping);

            this.logger.log(
              `Updated partner mapping for SSN_EXISTS: User ${userId} -> ${failure.ClientUserKey}`
            );
            return this.getAuthenticationQuestions(mapping.partnerCustomerId, loanApplicationId);
          } catch (error) {
            this.logger.error(`Failed to update mapping for SSN_EXISTS: ${error.message}`);
            return { success: false, error: 'Failed to update partner mapping for SSN_EXISTS' };
          }
        }
        return { success: false, error: 'SSN already exists but no ClientUserKey provided' };
      }
      case 'SSN_MISMATCH':
        return { success: false, error: 'SSN mismatch' };
      case 'FATAL':
      case 'FAILURE':
      default:
        this.logger.error(failure.Message);
        return { success: false, error: 'CIBIL enrollment failure' };
    }
  }

  private getSeparatedUserName(name: string) {
    const nameParts = name.trim().split(/\s+/);
    let forename;
    let surname;

    if (nameParts.length === 1) {
      forename = nameParts[0];
      surname = nameParts[0].substring(0, 3); // First 3 characters of name
    } else if (nameParts.length > 1) {
      forename = nameParts[0];
      surname = nameParts.slice(1).join(' ') || '';
    }
    return { forename, surname };
  }

  private async savePartnerCustomerMapping(
    userId: number,
    partnerCustomerId: string
  ): Promise<UserToPartnerCustomer> {
    try {
      // Create new record
      const newMapping = new UserToPartnerCustomer();
      newMapping.userId = userId;
      newMapping.partnerCustomerId = partnerCustomerId;
      const res = await this.userMappingRepository.save(newMapping);
      this.logger.log(`Created new partner mapping: User ${userId} -> ${res.partnerCustomerId}`);
      return res;
    } catch (error) {
      this.logger.error(`Failed to save partner mapping: ${error.message}`);
      throw new Error('Failed to save partner customer mapping');
    }
  }

  // Verify Answers with automatic credit score retrieval (FetchCibil)
  async verifyAnswers(answer: AnswerDto): Promise<VerificationResponse | Message> {
    // Get user ID from request context
    const userId = this.cls.get<number>('userId');

    // Get partner customer ID from mapping
    const partnerCustomer = await this.getPartnerCustomer(userId);
    if (!partnerCustomer) {
      throw new Error('User is not enrolled with CIBIL');
    }

    if (partnerCustomer.ivStatus === 'Success') {
      if (!answer.applicationId) {
        return { success: false, error: 'Application ID missing in request.' };
      }
      const loanApplication = await this.loanApplicationRepository.findOne({
        where: { id: answer.applicationId, userId },
      });
      if (!loanApplication) {
        return { success: false, error: 'Loan application not found for the user.' };
      }
      return this.getCreditReport(partnerCustomer.partnerCustomerId, answer.applicationId);
    }

    const partnerCustomerId = partnerCustomer.partnerCustomerId;

    const { questionId, answers } = answer;
    // Fetch the question log by ID
    const questionLog = await this.cibilQuestionLogRepo.findOne({
      where: { id: questionId },
    });

    if (!questionLog) {
      throw new Error('Question log not found');
    }

    const challengeGuid = questionLog.challengeGuid;
    const applicationId = questionLog.applicationId;

    if (!challengeGuid) {
      throw new Error('No active challenge found for verification');
    }

    const verificationPayload = {
      partnerCustomerId,
      challengeConfigGUID: challengeGuid, // Add challengeConfigGUID to payload
      answers,
      localeType: 'enIN',
    };

    // Step 1: Verify answers
    const verificationResponse = await this.postToCibil(
      'verify-authentication',
      verificationPayload
    );
    this.logger.debug('verify authentication response', verificationResponse);
    if (!verificationResponse.success) {
      await this.userMappingRepository.softDelete({ userId }); // Clean up mapping on failure
      return { success: false, error: 'Verification failed. Restart CIBIL verification' };
    }
    switch (verificationResponse.data) {
      case 'Success':
        await this.userMappingRepository.update(
          { partnerCustomerId },
          { ivStatus: verificationResponse.data as string }
        );
        return this.getCreditReport(partnerCustomerId, applicationId);
      case 'InProgress':
        await this.userMappingRepository.update(
          { partnerCustomerId },
          { ivStatus: verificationResponse.data as string }
        );
        return this.getAuthenticationQuestions(partnerCustomerId, applicationId);
      case 'Failure':
      default:
        await this.userMappingRepository.softDelete({ userId }); // Clean up mapping on failure
        return { success: false, error: 'Verification failed. Restart CIBIL verification' };
    }
  }

  async getCreditReport(partnerCustomerId: string, applicationId: number): Promise<Message> {
    const creditScore = await this.getCreditAnalysis(partnerCustomerId);
    const { creditAnalysis } = creditScore.data;

    const score = creditAnalysis.creditScore;
    const minScore = parseInt(process.env.MIN_CIBIL_SCORE || '700');

    if (score >= minScore) {
      await this.applicantDetailRepository.update({ applicationId }, { creditAnalysis });
      await this.dataSource.transaction(async (manager) => {
        await manager.update(LoanApplication, applicationId, {
          subStatus: ApplicationSubStatus.CIBIL_ACCEPTED,
        });

        await manager.save(ApplicationEventLog, {
          applicationId,
          eventType: ApplicationSubStatus.CIBIL_ACCEPTED,
        });
      });

      return {
        success: true,
        message: 'Verification successful!',
        data: {
          creditScore: score,
        },
      };
    } else {
      await this.applicantDetailRepository.update({ applicationId }, { creditAnalysis });
      await this.dataSource.transaction(async (manager) => {
        await manager.update(LoanApplication, applicationId, {
          subStatus: ApplicationSubStatus.BAD_CIBIL,
          statusId: 7,
        });
        await manager.save(ApplicationEventLog, {
          applicationId,
          eventType: ApplicationSubStatus.BAD_CIBIL,
        });
      });

      return {
        success: false,
        error: 'CIBIL score is too low.',
        data: {
          creditScore: score,
        },
      };
    }
  }

  private async getPartnerCustomer(userId: number): Promise<UserToPartnerCustomer | null> {
    try {
      const mapping = await this.userMappingRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' }, // Get the latest mapping
      });

      return mapping;
    } catch (error) {
      this.logger.error(`Failed to get partner customer ID for user ${userId}: ${error.message}`);
      throw new Error('Failed to retrieve partner customer mapping');
    }
  }

  getRegionIdFromState(state: string) {
    const normalizedState = state
      .replace(/\s+/g, '')
      .replace(/\./g, '')
      .replace(/'/g, '')
      .toLowerCase();

    const stateEntry = Object.entries(StateCode).find(
      ([key]) => key.toLowerCase() === normalizedState
    );

    return stateEntry ? stateEntry[1].toString() : null;
  }

  // Get Authentication Questions
  async getAuthenticationQuestions(
    partnerCustomerId: string,
    applicationId: number
  ): Promise<QuestionsResponse | Message> {
    const payload = {
      partnerCustomerId,
    };

    const response = (await this.postToCibil(
      'authentication-questions',
      payload
    )) as QuestionsResponse;
    if (response.data.Failure) {
      this.logger.error(`Getting Auth Question failure: ${response.data.Failure.Message}`);
      await this.userMappingRepository.softDelete({ partnerCustomerId });
      return {
        success: false,
        error: 'Auth Question failure. Please restart CIBIL verification',
      };
    }

    if (response.data.IVStatus === 'Success') {
      return {
        success: true,
        data: {
          question: [],
          queueName: 'FETCH_DIRECTLY',
        },
      };
    }

    // Extract challenge GUID and question data
    const { ChallengeConfigGUID: challengeGuid } = response.data;

    // Save to cibil_question_log table
    const questionId = await this.saveQuestionLog(
      challengeGuid!,
      response.data,
      partnerCustomerId,
      applicationId
    );

    // Remove ChallengeConfigGUID from response
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { ChallengeConfigGUID, ...responseWithoutGuid } = response.data;
    return { success: true, data: { ...responseWithoutGuid, questionId } };
  }

  private async saveQuestionLog(
    challengeGuid: string,
    questionData: Record<string, any>,
    partnerCustomerId: string,
    applicationId: number
  ): Promise<number> {
    try {
      // Create new log entry
      const logEntry = new CibilQuestionLog();
      logEntry.applicationId = applicationId;
      logEntry.pccId = partnerCustomerId;
      logEntry.challengeGuid = challengeGuid;
      logEntry.question = questionData;

      const savedLog = await this.cibilQuestionLogRepo.save(logEntry);
      this.logger.log(`Saved CIBIL question log for application ${applicationId}`);
      return savedLog.id; // Return saved entity with ID
    } catch (error) {
      this.logger.error(`Failed to save CIBIL question log: ${error.message}`, error.stack);
      throw error;
    }
  }

  /** Commenting code
   
  // Get Product Web Token
  async getProductWebToken(partnerCustomerId: string): Promise<TokenResponse> {
    const payload = {
      partnerCustomerId,
    };

    return this.postToCibil('product-web-token', payload);
  }
**/
  // Get Credit Score
  async getCreditAnalysis(partnerCustomerId: string): Promise<CustomerAssetsResponse> {
    const payload = {
      partnerCustomerId,
      numberOfCreditEnquiries: true,
      limitUtilizationRange: true,
      paymentOnTimeRatio: true,
      newAccountsMonths: 6,
      newDelinquentAccountsMonths: 3,
      includeCreditScore: true,
    };

    return this.postToCibil('customer-assets', payload);
  }

  // Private method to handle all POST requests
  private async postToCibil(endpoint: string, data: object): Promise<Message> {
    const url = `${this.baseUrl}/${endpoint}`;

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, data, {
          headers: {
            Authorization: this.cls.get<Record<string, string>>('header')?.['authorization'],
            'Content-Type': 'application/json',
          },
        })
      );
      return response.data as Message;
    } catch (error) {
      this.handleCibilError(error as AxiosError, endpoint);
    }
  }

  // Error handling
  private handleCibilError(error: AxiosError, endpoint: string): never {
    if (error.response) {
      throw new Error(
        `CIBIL API error (${endpoint}): ${error.response.status} - ${JSON.stringify((error.response.data as any)?.data)}`
      );
    } else if (error.request) {
      throw new Error(`CIBIL service unreachable for ${endpoint}: ${error.message}`);
    } else {
      throw new Error(`Request setup failed for ${endpoint}: ${error.message}`);
    }
  }
}
