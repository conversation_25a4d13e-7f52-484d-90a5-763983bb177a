import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity({ name: 'application_mandates' })
export class ApplicationMandate extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @Column({ name: 'txn_id', type: 'varchar', length: 50 })
  txnId: string;

  @Column({ name: 'status', type: 'varchar', nullable: true })
  status: string;
}
