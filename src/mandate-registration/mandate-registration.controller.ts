import { Controller, Get, UseGuards } from '@nestjs/common';
import { MandateRegistrationService } from './mandate-registration.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('mandate-registration')
export class MandateRegistrationController {
  constructor(private readonly mandateService: MandateRegistrationService) {}

  @Get()
  async initiateMandate() {
    return fixed_response(await this.mandateService.initiateMandate());
  }

  @Get('status')
  async initiateMandateStatus() {
    return fixed_response(await this.mandateService.initiateMandateStatus());
  }
}
