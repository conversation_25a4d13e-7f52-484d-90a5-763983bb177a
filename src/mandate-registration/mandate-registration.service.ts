import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { ClsService } from 'nestjs-cls';
import { firstValueFrom } from 'rxjs';
import { Address, AddressType } from 'src/addresses/entity/address.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { LoanOffer, LoanOfferType } from 'src/loan-offer/entity/loan-offer.entity';
import { Message } from 'src/utils/response-util';
import { DataSource, Repository } from 'typeorm';
import { ApplicationMandate } from './entity/mandate-registration.entity';
import { ApplicationEventLog } from 'src/application-event-log/entity/application-event-log.entity';

@Injectable()
export class MandateRegistrationService {
  private readonly logger = new Logger(MandateRegistrationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly cls: ClsService,
    @InjectRepository(LoanApplication)
    private readonly loanApplicationRepository: Repository<LoanApplication>,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    @InjectRepository(BankAccount)
    private readonly bankRepository: Repository<BankAccount>,
    @InjectRepository(ApplicationMandate)
    private readonly mandateRepo: Repository<ApplicationMandate>,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async initiateMandate(): Promise<Message> {
    const userId = this.cls.get<number>('userId');
    const loan = await this.loanApplicationRepository.findOne({
      where: {
        userId,
        subStatus: ApplicationSubStatus.BANK_ACCOUNT_VERIFIED,
      },
      relations: ['applicantDetail', 'offers'],
    });

    if (!loan || !loan.applicantDetail) {
      return {
        success: false,
        error: 'loan not found',
      };
    }

    const address = await this.addressRepository.findOne({
      where: {
        linkedEntityType: 'applicant_details',
        linkedEntityId: loan.applicantDetail.id,
        type: AddressType.PERMANENT,
      },
    });

    const bank = await this.bankRepository.findOne({
      where: {
        linkedEntityType: 'loan_applications',
        linkedEntityId: loan.id,
      },
    });

    const baseUrl = this.configService.getOrThrow<string>('MANDATE_SERVICE_BASE_URL');
    const transactionId = Date.now().toString().slice(-10);
    const mandate = this.mandateRepo.create({
      applicationId: loan.id,
      txnId: transactionId,
    });
    await this.mandateRepo.save(mandate);
    const clientId = this.configService.getOrThrow<string>('MANDATE_CLIENT_ID');
    const today = new Date();
    const nextMonth = new Date(today);
    nextMonth.setMonth(today.getMonth() + 1);
    const sbNextRundate = nextMonth.toLocaleDateString('en-GB');
    const filterOfferLoan: LoanOffer | undefined = loan.offers.find(
      (r: LoanOffer) => r.type == LoanOfferType.USER_ACCEPTED
    );
    const payload = {
      transactionId,
      applicationId: loan.applicationRefNum,
      buyerEmail: loan?.applicantDetail?.email || '',
      buyerPhone: loan?.applicantDetail?.mobile || '',
      buyerFirstName: loan?.applicantDetail?.name || '',
      buyerLastName: '',
      buyerAddress: address?.addressLine1 + '' + address?.addressLine2,
      buyerCity: address?.city,
      buyerState: address?.state,
      buyerCountry: '',
      buyerPinCode: address?.pincode,
      amount: '1.11',
      sbNextRundate,
      sbAccnumber: bank?.accountNumber,
      sbIfsc: bank?.ifsc,
      sbBankCode: bank?.ifsc.slice(0, 4),
      sbMaxAmount: '100000.00',
      sbAmount: filterOfferLoan?.amount,
      sbFrequency: '1',
      sbPeriod: 'M',
      currency: '356',
      isocurrency: 'INR',
      paymode: 'token',
      sbIsRecurring: '1',
      sbRecurringCount: filterOfferLoan?.tenureInMonths.toString(),
      sbRetryAttempts: '0',
      txnSubType: '12',
      UID: '',
      arpyVer: '3',
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(`${baseUrl}/api/mandate/generate-link`, payload, {
          headers: {
            Authorization: this.cls.get('header')?.['authorization'],
            clientId,
          },
        })
      );

      return {
        success: true,
        data: response.data.data,
      };
    } catch (error) {
      this.logger.error('Failed to fetch mandate-registration:', error?.response.data.error);
      console.log(error?.response.data.errors, 'error');
      return {
        success: false,
        error: error?.response?.data?.message || 'Mandate API failed',
      };
    }
  }

  async initiateMandateStatus(): Promise<Message> {
    const baseUrl = this.configService.getOrThrow<string>('MANDATE_SERVICE_BASE_URL');
    const userId = this.cls.get<number>('userId');
    const clientId = this.configService.getOrThrow<string>('MANDATE_CLIENT_ID');

    const loan = await this.loanApplicationRepository.findOne({
      where: {
        userId,
        subStatus: ApplicationSubStatus.BANK_ACCOUNT_VERIFIED,
      },
    });

    if (!loan) {
      return {
        success: false,
        error: 'Loan not found',
      };
    }

    const mandate = await this.mandateRepo.findOne({
      where: {
        applicationId: loan.id,
      },
    });

    if (!mandate) {
      return {
        success: false,
        error: 'Mandate not found',
      };
    }

    const payload = {
      transactionId: mandate.txnId,
      applicationId: loan.applicationRefNum,
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(`${baseUrl}/api/mandate/status`, payload, {
          headers: {
            Authorization: this.cls.get('header')?.['authorization'],
            clientId,
          },
        })
      );

      if (response.data?.success && response.data?.status) {
        const { status } = response.data;
        await this.dataSource.transaction(async (manager) => {
          await manager.update(ApplicationMandate, mandate.id, { status });
          if (status == 'SUCCESS') {
            await manager.update(LoanApplication, loan.id, {
              subStatus: ApplicationSubStatus.NACH_MANDATE,
            });
            await manager.save(ApplicationEventLog, {
              applicationId: loan.id,
              eventType: ApplicationSubStatus.NACH_MANDATE,
            });
          }
        });

        return {
          success: true,
          data: {
            message: 'Mandate status updated',
            status: mandate.status,
          },
        };
      }

      return {
        success: false,
        error: 'Mandate status update failed or status missing in response',
      };
    } catch (error) {
      this.logger.error('Failed to fetch mandate-registration:', error?.response?.data?.error);
      return {
        success: false,
        error: error?.response?.data?.message || 'Mandate API failed',
      };
    }
  }
}
