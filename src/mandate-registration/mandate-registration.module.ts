import { Module } from '@nestjs/common';
import { MandateRegistrationService } from './mandate-registration.service';
import { MandateRegistrationController } from './mandate-registration.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Address } from 'src/addresses/entity/address.entity';
import { BankAccount } from 'src/bank-account/entity/bank-account.entity';
import { ApplicationMandate } from './entity/mandate-registration.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([LoanApplication, Address, BankAccount, ApplicationMandate]),
  ],
  controllers: [MandateRegistrationController],
  providers: [MandateRegistrationService],
})
export class MandateRegistrationModule {}
