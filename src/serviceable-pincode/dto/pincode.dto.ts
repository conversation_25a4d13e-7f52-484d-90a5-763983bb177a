import { Transform } from 'class-transformer';
import { IsNotEmpty, IsBoolean, IsOptional, IsNumber, Matches } from 'class-validator';

export class PincodeDto {
  @IsNotEmpty()
  @Transform(({ value }) => String(value))
  @Matches(/^\d{5,6}$/, { message: 'Pincode must be 5–6 digits' })
  pincode: string;

  @IsNumber()
  @IsNotEmpty()
  productId: number;

  @IsOptional()
  @IsBoolean()
  metadata: boolean;
}
