import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { Product } from 'src/product/entity/product.entity';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity({ name: 'servicable_pincodes' })
export class ServiceablePincode extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'pincode', type: 'varchar', length: 10 })
  pincode: string;

  @Column({ name: 'country_code', type: 'varchar', length: 2 })
  countryCode: string;

  @Column({ name: 'product_id', type: 'int' })
  productId: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
