import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceablePincode } from './entity/serviceable-pincode.entity';
import { ServiceablePincodeService } from './serviceable-pincode.service';
import { ServiceablePincodeController } from './serviceable-pincode.controller';
import { Product } from 'src/product/entity/product.entity';
import { Lead } from 'src/leads/entity/lead.entity';
import { PincodeMaster } from 'src/pincode-master/entity/pincode-master.entity';
import { PincodeMasterModule } from 'src/pincode-master/pincode-master.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ServiceablePincode, Product, Lead, PincodeMaster]),
    PincodeMasterModule,
  ],
  providers: [ServiceablePincodeService],
  controllers: [ServiceablePincodeController],
})
export class ServiceablePincodeModule {}
