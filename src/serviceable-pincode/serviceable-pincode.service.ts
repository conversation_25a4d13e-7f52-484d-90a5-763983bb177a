import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PincodeDto } from './dto/pincode.dto';
import { ServiceablePincode } from './entity/serviceable-pincode.entity';
import { Lead } from 'src/leads/entity/lead.entity';
import { Message } from 'src/utils/response-util';
import { PincodeMasterService } from 'src/pincode-master/pincode-master.service';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class ServiceablePincodeService {
  constructor(
    @InjectRepository(ServiceablePincode)
    private readonly pincodeRepository: Repository<ServiceablePincode>,
    @InjectRepository(Lead)
    private readonly leadsRepository: Repository<Lead>,
    private readonly cls: ClsService,
    private readonly pincodeService: PincodeMasterService
  ) {}

  async isServiceAvailable(dto: PincodeDto): Promise<Message> {
    const serviceable = await this.pincodeRepository.findOne({
      where: {
        pincode: dto.pincode,
        productId: dto.productId,
      },
    });

    if (!serviceable) {
      await this.leadsRepository.save({
        userId: this.cls.get<number>('userId'),
        pincode: dto.pincode,
        loanTypeInquired: dto.productId,
      });

      return {
        success: false,
        error: 'Pincode is not serviceable',
      };
    }

    if (dto.metadata === true) {
      const res = await this.checkPincode(dto.pincode);
      if (res.success)
        return { success: true, data: { ...res.data, serviceable: true }, message: res.message };
      return res;
    }

    return {
      success: true,
      message: 'Pincode is serviceable',
    };
  }

  async checkPincode(pincode: string) {
    return this.pincodeService.checkPincode(pincode);
  }
}
