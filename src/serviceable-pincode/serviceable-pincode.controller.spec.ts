import { Test, TestingModule } from '@nestjs/testing';
import { ServiceablePincodeController } from './serviceable-pincode.controller';

describe('ServiceablePincodeController', () => {
  let controller: ServiceablePincodeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ServiceablePincodeController],
    }).compile();

    controller = module.get<ServiceablePincodeController>(ServiceablePincodeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
