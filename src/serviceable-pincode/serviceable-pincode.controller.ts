import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { PincodeDto } from './dto/pincode.dto';
import { ServiceablePincodeService } from './serviceable-pincode.service';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('serviceable-pincode')
export class ServiceablePincodeController {
  constructor(private readonly pincodeService: ServiceablePincodeService) {}

  @Post('check')
  async check(@Body() dto: PincodeDto) {
    return fixed_response(await this.pincodeService.isServiceAvailable(dto));
  }
}
