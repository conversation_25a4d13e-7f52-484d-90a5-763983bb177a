import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Product } from 'src/product/entity/product.entity';
import { Repository } from 'typeorm';
import { FrozenAccount } from './entity/frozen-account.entity';
import { FrozenAccountDto, SalaryMode } from './dto/frozen-account.dto';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class FrozenAccountService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,

    @InjectRepository(FrozenAccount)
    private readonly frozenRepo: Repository<FrozenAccount>,
    private readonly cls: ClsService
  ) {}

  async verifySalaryMode(dto: FrozenAccountDto): Promise<Message> {
    const { salarymode, productId } = dto;
    const userId = this.cls.get<number>('userId');

    const product = await this.productRepo.findOne({ where: { id: productId } });
    if (!product) {
      return { success: false, error: `Product with id ${productId} not found` };
    }

    if (salarymode === SalaryMode.BANK) {
      return {
        success: true,
        message: 'You can proceed with the application',
      };
    }

    const frozen = this.frozenRepo.create({
      pan: 'STATICPAN',
      frozenTillDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      productId: product.id,
      userId,
    });

    await this.frozenRepo.save(frozen);

    return {
      success: false,
      error:
        'We regret inform you that we are unable to process your loan application this time as it dose not meet eligibility norm. Please reapply after 90 days.',
    };
  }
}
