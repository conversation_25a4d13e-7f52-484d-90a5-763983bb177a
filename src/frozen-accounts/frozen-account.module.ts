import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from 'src/product/entity/product.entity';
import { FrozenAccount } from './entity/frozen-account.entity';
import { FrozenAccountController } from './frozen-account.controller';
import { FrozenAccountService } from './frozen-account.service';

@Module({
  imports: [TypeOrmModule.forFeature([Product, FrozenAccount])],
  controllers: [FrozenAccountController],
  providers: [FrozenAccountService],
  exports: [FrozenAccountService],
})
export class FrozenAccountModule {}
