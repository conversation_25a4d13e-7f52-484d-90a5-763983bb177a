import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { Product } from 'src/product/entity/product.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm';

@Entity({ name: 'frozen_accounts' })
export class FrozenAccount extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 10 })
  pan: string;

  @Column({ name: 'frozen_till_date', type: 'timestamptz' })
  frozenTillDate: Date;

  @Column({ name: 'product_id' })
  productId: number;

  @ManyToOne(() => Product)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'override_remarks', type: 'text', nullable: true })
  overrideRemarks?: string;

  @Column({ name: 'reason', type: 'varchar', nullable: true })
  reason?: string;
}
