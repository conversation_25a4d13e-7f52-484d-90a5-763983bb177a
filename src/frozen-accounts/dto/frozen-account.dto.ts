import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsN<PERSON>ber } from 'class-validator';

export enum SalaryMode {
  CASH = 'CASH',
  CHEQUE = 'CHEQUE',
  BANK = 'BANK',
}

export class FrozenAccountDto {
  @IsNotEmpty()
  @IsEnum(SalaryMode, {
    message: 'Only CASH, CHEQUE or BANK allowed',
  })
  salarymode: SalaryMode;

  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber({}, { message: 'productId must be a number' })
  productId: number;
}
