import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BankAccountService } from './bank-account.service';
import { BankAccountController } from './bank-account.controller';
import { BankAccount } from './entity/bank-account.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';

@Module({
  imports: [HttpModule, TypeOrmModule.forFeature([BankAccount, LoanApplication])],
  providers: [BankAccountService],
  controllers: [BankAccountController],
})
export class BankAccountModule {}
