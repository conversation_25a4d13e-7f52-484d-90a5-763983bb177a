import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity({ name: 'bank_accounts' })
@Index(['entityType', 'entityId'])
export class BankAccount extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'entity_type', type: 'varchar', length: 50 })
  entityType: string;

  @Column({ name: 'entity_id', type: 'int' })
  entityId: number;

  @Column({ name: 'bank_name', type: 'varchar', length: 100 })
  bankName: string;

  @Column({ name: 'customer_name', type: 'varchar', length: 100 })
  customerName: string;

  @Column({ name: 'ifsc', type: 'varchar', length: 11, nullable: true })
  ifsc: string;

  @Column({ name: 'account_number', type: 'varchar', length: 34 })
  accountNumber: string;

  @Column({ name: 'acc_type', type: 'varchar', length: 20 })
  accType: string;

  @Column({ name: 'is_verified', default: false })
  isVerified: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'linked_entity_type', type: 'varchar', nullable: true })
  linkedEntityType: string;

  @Column({ name: 'linked_entity_id', type: 'int', nullable: true })
  linkedEntityId: number;
}
