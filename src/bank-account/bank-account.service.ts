import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { BankAccount } from './entity/bank-account.entity';
import { DataSource, Repository } from 'typeorm';
import { VerifyBankAccountDto } from './dto/verify-bank-account.dto';
import { Message } from 'src/utils/response-util';
import { firstValueFrom } from 'rxjs';
import { ClsService } from 'nestjs-cls';
import {
  ApplicationSubStatus,
  LoanApplication,
} from 'src/loan-application/entity/loan-application.entity';
import { FrozenAccount } from 'src/frozen-accounts/entity/frozen-account.entity';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';

@Injectable()
export class BankAccountService {
  private readonly logger = new Logger(BankAccountService.name);

  constructor(
    private readonly http: HttpService,
    private readonly config: ConfigService,
    @InjectRepository(BankAccount)
    private readonly accountRepository: Repository<BankAccount>,
    private readonly cls: ClsService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async verifyBankAccount(dto: VerifyBankAccountDto, id: number): Promise<Message> {
    const userId = this.cls.get<number>('userId');
    const bankAccount = await this.accountRepository.findOne({
      where: { id, entityType: 'user', entityId: userId },
    });

    if (!bankAccount) {
      return { success: false, error: 'Unauthorised access or bank account not found' };
    }

    try {
      const payload = {
        accountNumber: dto.accountNumber + '' + bankAccount.accountNumber,
        ifsc: dto.ifsc,
        customerName: bankAccount.customerName,
      };

      const kycBaseUrl = this.config.getOrThrow<string>('KYC_SERVICE_BASE_URL');
      const resp = await firstValueFrom(
        this.http.post(`${kycBaseUrl}/api/account-verification`, payload, {
          headers: {
            Authorization: this.cls.get<Record<string, string>>('header')?.['authorization'],
          },
        })
      );

      const kycData = resp.data?.data;

      if (!kycData) {
        await this.dataSource.transaction(async (manager) => {
          await manager.update(
            LoanApplication,
            { id: bankAccount.linkedEntityId },
            {
              statusId: 7,
              subStatus: ApplicationSubStatus.BANK_VERIFICATION_FAILED,
            }
          );
          const frozenTillDate: Date = new Date();
          frozenTillDate.setDate(frozenTillDate.getDate() + 90);
          const applicant = await manager.findOne(ApplicantDetail, {
            where: { applicationId: bankAccount.linkedEntityId },
          });
          await manager.save(FrozenAccount, {
            pan: applicant!.pan,
            userId: bankAccount.entityId,
            frozenTillDate,
            reason: `Application #${bankAccount.linkedEntityId} Rejected`,
          });
        });

        this.logger.warn('KYC service returned no data');
        return { success: false, error: 'Bank verification failed' };
      }

      await this.dataSource.transaction(async (manager) => {
        await manager.update(
          LoanApplication,
          { id: bankAccount.linkedEntityId },
          {
            subStatus: ApplicationSubStatus.BANK_ACCOUNT_VERIFIED,
          }
        );

        bankAccount.accountNumber = dto.accountNumber + '' + bankAccount.accountNumber;
        bankAccount.ifsc = dto.ifsc;
        bankAccount.isVerified = true;

        await manager.getRepository(BankAccount).save(bankAccount);
      });

      return {
        success: true,
        data: {},
        message: 'Bank account verified successfully',
      };
    } catch (err) {
      this.logger.error('Create Bank Account Error:', err);
      return {
        success: false,
        error: 'Unable to create bank account',
      };
    }
  }

  async getBankAccountDetails(applicationId: number): Promise<Message> {
    const userId = this.cls.get<number>('userId');
    const account = await this.accountRepository.findOne({
      where: {
        entityType: 'user',
        entityId: userId,
        isVerified: false,
        linkedEntityType: 'loan_applications',
        linkedEntityId: applicationId,
      },
    });
    if (!account) return { success: false, error: 'No bank account found' };
    return { success: true, data: account, message: 'Bank account details fetched' };
  }
}
