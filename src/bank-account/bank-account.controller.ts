import { Body, Controller, Get, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { BankAccountService } from './bank-account.service';
import { VerifyBankAccountDto } from './dto/verify-bank-account.dto';
import { fixed_response } from 'src/utils/response-util';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@Controller('bank-account')
@UseGuards(DecodeTokenGuard)
export class BankAccountController {
  constructor(private readonly accountService: BankAccountService) {}

  @Post('verify/:id')
  async verifyBankAccount(
    @Body() dto: VerifyBankAccountDto,
    @Param('id', ParseIntPipe) id: number
  ) {
    return fixed_response(await this.accountService.verifyBankAccount(dto, id));
  }

  @Get()
  async getAccountDetails(@Query('applicationId', ParseIntPipe) applicationId: number) {
    return fixed_response(await this.accountService.getBankAccountDetails(applicationId));
  }
}
