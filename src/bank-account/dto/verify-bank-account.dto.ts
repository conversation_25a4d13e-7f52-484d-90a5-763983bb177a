import { IsString, IsNotEmpty, Length, Matches, IsEnum, IsOptional } from 'class-validator';

export enum BankAccountType {
  SAVINGS = 'SAVINGS',
  CURRENT = 'CURRENT',
}

export class VerifyBankAccountDto {
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @IsString()
  @IsOptional()
  @Length(11, 11, { message: 'IFSC code must be exactly 11 characters long' })
  @Matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, {
    message:
      'IFSC must follow the format: 4 letters, a 0, then 6 letters or digits (e.g. “ABCD0A1B2C3”)',
  })
  ifsc: string;

  // @IsString()
  // @IsOptional()
  // customerName: string;

  // @IsString()
  // @IsOptional()
  // bankName: string;

  // @IsOptional()
  // @IsEnum(BankAccountType, {
  //   message: `accType must be one of: ${Object.values(BankAccountType).join(', ')}`,
  // })
  // accType: BankAccountType;
}
