import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
import { CustomBaseEntity } from 'src/common/entity/base.entity';

@Entity({ name: 'business_details' })
export class BusinessDetail extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_name', type: 'varchar' })
  companyName: string;

  @Column({ name: 'nature', type: 'varchar' })
  nature: string;

  @Column({ name: 'doi', type: 'date' })
  doi: Date;

  @Column({ name: 'type', type: 'varchar' })
  type: string;

  @Column({ name: 'udyog', type: 'varchar' })
  udyog: string;

  @Column({ name: 'registration', type: 'varchar' })
  registration: string;

  @Column({ name: 'website', type: 'varchar' })
  website: string;

  @Column({ name: 'business_email', type: 'varchar' })
  businessEmail: string;

  @Column({ name: 'authorised_signatory', type: 'varchar' })
  authorisedSignatory: string;

  @Column({ name: 'cin', type: 'varchar' })
  cin: string;

  @Column({ name: 'annual_income', type: 'decimal', precision: 15, scale: 2 })
  annualIncome: number;

  @Column({ name: 'employee_count', type: 'int' })
  employeeCount: number;

  @Column({ name: 'entity_type', type: 'varchar' })
  entityType: string;

  @Column({ name: 'entity_id', type: 'int' })
  entityId: number;

  @Column({ name: 'contact_number', type: 'varchar', length: 10 })
  contactNumber: string;
}
