import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';

@Entity('business_directors')
export class BusinessDirector extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id' })
  applicationId: number;

  @Column({ name: 'mobile', nullable: true })
  mobile?: string;

  @Column({ name: 'email', nullable: true })
  email?: string;

  @Column({ name: 'name' })
  name?: string;

  @Column({ name: 'pan', nullable: true })
  pan?: string;

  @Column({ name: 'din' })
  din?: string;

  @ManyToOne(() => LoanApplication, (application) => application.directors)
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;
}
