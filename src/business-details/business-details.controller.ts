import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { BusinessDetailsService } from './business-details.service';
import { fixed_response } from 'src/utils/response-util';
import {
  ArchitectDto,
  IcaiDto,
  IcsiDto,
  IcwaifDto,
  IcwaimDto,
  NmcDto,
  PanProfileDto,
  UdyamRegistrationDto,
} from './dto/business-detail.dto';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';

@UseGuards(DecodeTokenGuard)
@Controller('business-details')
export class BusinessDetailsController {
  constructor(private readonly businessDetailsService: BusinessDetailsService) {}

  @Post('nmc')
  async verifyNmc(@Body() dto: NmcDto) {
    return fixed_response(await this.businessDetailsService.nmcVerify(dto));
  }

  @Post('icwaim')
  async verifyIcwaim(@Body() dto: IcwaimDto) {
    return fixed_response(await this.businessDetailsService.icwaimVerify(dto));
  }

  @Post('architect')
  async verifyArchitect(@Body() dto: ArchitectDto) {
    return fixed_response(await this.businessDetailsService.architectVerify(dto));
  }

  @Post('icai')
  async verifyIcai(@Body() dto: IcaiDto) {
    return fixed_response(await this.businessDetailsService.icaiVerify(dto));
  }

  @Post('icsi')
  async verifyIcsi(@Body() dto: IcsiDto) {
    return fixed_response(await this.businessDetailsService.icsiVerify(dto));
  }

  @Post('icwaif')
  async verifyIcwaif(@Body() dto: IcwaifDto) {
    return fixed_response(await this.businessDetailsService.icwaifVerify(dto));
  }

  @Post('pan')
  async getProfile(@Body() dto: PanProfileDto) {
    return fixed_response(await this.businessDetailsService.panProfile(dto));
  }

  @Post('udyam')
  async getUdyam(@Body() dto: UdyamRegistrationDto) {
    return fixed_response(await this.businessDetailsService.udyamRegistration(dto));
  }

  @Get('directors')
  async getDirectors() {
    return fixed_response(await this.businessDetailsService.getDirectors());
  }
}
