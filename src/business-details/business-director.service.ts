import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessDirector } from './entity/business-director.entity';
import { UpdateBusinessDirectorDto } from './dto/business-director.dto';
import { Message } from 'src/utils/response-util';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class BusinessDirectorsService {
  constructor(
    @InjectRepository(BusinessDirector)
    private readonly directorsRepo: Repository<BusinessDirector>,
    private readonly cls: ClsService
  ) {}

  async update(id: number, dto: UpdateBusinessDirectorDto): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const director = await this.directorsRepo
        .createQueryBuilder('director')
        .innerJoin(
          'loan_applications',
          'loan',
          'loan.id = director.application_id AND loan.user_id = :userId',
          { userId }
        )
        .where('director.id = :id', { id })
        .getOne();

      if (!director) {
        return {
          success: false,
          error: 'Director not found',
        };
      }

      Object.assign(director, dto);
      const updated = await this.directorsRepo.save(director);

      return {
        success: true,
        data: updated.id,
        message: 'Director updated successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update director',
      };
    }
  }
}
