import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ClsService } from 'nestjs-cls';
import { ConfigService } from '@nestjs/config';
import {
  NmcDto,
  IcwaimDto,
  ArchitectDto,
  IcaiDto,
  IcsiDto,
  IcwaifDto,
  PanProfileDto,
  UdyamRegistrationDto,
} from './dto/business-detail.dto';
import { Message } from 'src/utils/response-util';
import { InjectRepository } from '@nestjs/typeorm';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Repository } from 'typeorm';
import { BusinessDirector } from './entity/business-director.entity';
import { BusinessDetail } from './entity/business-details.entity';

@Injectable()
export class BusinessDetailsService {
  private readonly logger = new Logger(BusinessDetailsService.name);
  private readonly kycBaseUrl: string;

  constructor(
    private readonly http: HttpService,
    private readonly cls: ClsService,
    private readonly config: ConfigService,
    @InjectRepository(LoanApplication)
    private readonly loanAppRepo: Repository<LoanApplication>,
    @InjectRepository(BusinessDetail)
    private readonly businessDetailRepo: Repository<BusinessDetail>,
    @InjectRepository(BusinessDirector)
    private readonly directorsRepo: Repository<BusinessDirector>
  ) {
    this.kycBaseUrl = this.config.getOrThrow<string>('KYC_SERVICE_BASE_URL');
  }

  async nmcVerify(dto: NmcDto): Promise<Message> {
    try {
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/nmc-membership-authentication`, dto, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'NMC verification failed' };

      return { success: true, data, message: 'NMC verification successful' };
    } catch (err) {
      this.logger.error('NMC verification error:', err);
      return { success: false, error: 'Unable to verify NMC' };
    }
  }

  async icwaimVerify(dto: IcwaimDto): Promise<Message> {
    try {
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/icwai-membership-authentication`, dto, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'ICWAIM verification failed' };

      return { success: true, data, message: 'ICWAIM verification successful' };
    } catch (err) {
      this.logger.error('ICWAIM verification error:', err);
      return { success: false, error: 'Unable to verify ICWAIM' };
    }
  }

  async architectVerify(dto: ArchitectDto): Promise<Message> {
    try {
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/architect-authentication`, dto, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'Architect verification failed' };

      return { success: true, data, message: 'Architect verification successful' };
    } catch (err) {
      this.logger.error('Architect verification error:', err);
      return { success: false, error: 'Unable to verify Architect' };
    }
  }

  async icaiVerify(dto: IcaiDto): Promise<Message> {
    try {
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/ca-membership-authentication`, dto, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'ICAI verification failed' };

      return { success: true, data, message: 'ICAI verification successful' };
    } catch (err) {
      this.logger.error('ICAI verification error:', err);
      return { success: false, error: 'Unable to verify ICAI' };
    }
  }

  async icsiVerify(dto: IcsiDto): Promise<Message> {
    try {
      const payload = { membershipNo: dto.membershipNo };
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/icsi`, payload, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'ICSI verification failed' };

      return { success: true, data, message: 'ICSI verification successful' };
    } catch (err) {
      this.logger.error('ICSI verification error:', err);
      return { success: false, error: 'Unable to verify ICSI' };
    }
  }

  async icwaifVerify(dto: IcwaifDto): Promise<Message> {
    try {
      const payload = { reg_no: dto.regNo };
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/icwaif`, payload, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'ICWAIF verification failed' };

      return { success: true, data, message: 'ICWAIF verification successful' };
    } catch (err) {
      this.logger.error('ICWAIF verification error:', err);
      return { success: false, error: 'Unable to verify ICWAIF' };
    }
  }

  async panProfile(dto: PanProfileDto): Promise<Message> {
    try {
      const payload = { pan: dto.pan };
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/pan-profile`, payload, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'GST search failed' };

      return { success: true, data, message: 'GST search successful' };
    } catch (err) {
      this.logger.error('GST search error:', err);
      return { success: false, error: 'Unable to search GST' };
    }
  }

  async udyamRegistration(dto: UdyamRegistrationDto): Promise<Message> {
    try {
      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/udyam-registration`, dto, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data) return { success: false, error: 'udyam registration failed' };

      return { success: true, data, message: 'udyam registration successful' };
    } catch (err) {
      this.logger.error('GST search error:', err);
      return { success: false, error: 'Unable to search GST' };
    }
  }

  async getDirectors(): Promise<Message> {
    try {
      const userId = this.cls.get<number>('userId');
      const loanApp = await this.loanAppRepo.findOne({
        where: { userId, productId: 2 },
      });

      if (!loanApp) {
        return { success: false, error: 'Loan application not found for user' };
      }

      const businessDetail = await this.businessDetailRepo.findOne({
        where: { entityId: loanApp.id },
      });

      if (!businessDetail) {
        return { success: false, error: 'Business details not found for user' };
      }

      const payload = { cin: businessDetail.cin };

      const resp = await firstValueFrom(
        this.http.post(`${this.kycBaseUrl}/api/mca-signatories`, payload, {
          headers: { Authorization: this.cls.get('header')?.['authorization'] },
        })
      );

      const data = resp.data?.data;
      if (!data?.length) {
        return { success: false, error: 'No directors found from MCA API' };
      }
      const directors = await this.directorsRepo.save(
        data.map((d: any) => ({
          applicationId: loanApp.id,
          name: d.full_name,
          din: d['DIN/DPIN/PAN'],
        }))
      );

      return {
        success: true,
        data: directors,
        message: 'Directors fetched successfully',
      };
    } catch (err) {
      this.logger.error('Get Directors error:', err);
      return { success: false, error: 'Unable to fetch directors' };
    }
  }
}
