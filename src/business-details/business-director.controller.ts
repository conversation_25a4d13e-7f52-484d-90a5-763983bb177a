import { Controller, Put, Param, Body, ParseIntPipe } from '@nestjs/common';
import { UpdateBusinessDirectorDto } from './dto/business-director.dto';
import { BusinessDirectorsService } from './business-director.service';
import { fixed_response } from 'src/utils/response-util';

@Controller('business-directors')
export class BusinessDirectorsController {
  constructor(private readonly directorsService: BusinessDirectorsService) {}

  @Put(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateBusinessDirectorDto) {
    return fixed_response(await this.directorsService.update(id, dto));
  }
}
