import { IsString, IsBoolean, IsNotEmpty, Matches, IsOptional } from 'class-validator';

export class NmcDto {
  @IsNotEmpty()
  @IsString()
  registrationNo: string;

  @IsNotEmpty()
  @IsString()
  yearOfReg: string;

  @IsNotEmpty()
  @IsString()
  medicalCouncil: string;
}

export class IcwaimDto {
  @IsString()
  membershipNo: string;
}

export class ArchitectDto {
  @IsString()
  regNo: string;
}

export class IcaiDto {
  @IsString()
  membershipNo: string;

  @IsBoolean()
  contactDetails: boolean;
}

export class IcsiDto {
  @IsString()
  membershipNo: string;
}

export class IcwaifDto {
  @IsString()
  regNo: string;
}

export class PanProfileDto {
  @IsNotEmpty()
  @IsString()
  @Matches(/^[A-Z]{5}\d{4}[A-Z]$/, {
    message:
      'PAN must be in the format: 5 uppercase letters, followed by 4 digits, and ending with 1 uppercase letter (e.g. **********)',
  })
  pan: string;
}

export class UdyamRegistrationDto {
  @IsNotEmpty()
  @IsString()
  udyamRegistrationNo: string;

  @IsOptional()
  @IsString()
  isPDFRequired: string;

  @IsOptional()
  @IsString()
  getEnterpriseDetails: string;
}
