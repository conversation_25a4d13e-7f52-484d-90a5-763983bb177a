import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { BusinessDetailsService } from './business-details.service';
import { BusinessDetailsController } from './business-details.controller';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessDirector } from './entity/business-director.entity';
import { BusinessDirectorsController } from './business-director.controller';
import { BusinessDirectorsService } from './business-director.service';
import { BusinessDetail } from './entity/business-details.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([LoanApplication, BusinessDirector, BusinessDetail]),
  ],
  controllers: [BusinessDetailsController, BusinessDirectorsController],
  providers: [BusinessDetailsService, BusinessDirectorsService],
})
export class BusinessDetailsModule {}
