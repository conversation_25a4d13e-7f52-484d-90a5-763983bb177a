import { DataSource } from 'typeorm';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { Product } from 'src/product/entity/product.entity';
import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';

export class LoanApplicationSeeder {
  async run(dataSource: DataSource): Promise<LoanApplication> {
    const loanAppRepo = dataSource.getRepository(LoanApplication);
    const productRepo = dataSource.getRepository(Product);
    const loanPurposeRepo = dataSource.getRepository(LoanPurposeMaster);

    const product = await productRepo.findOne({ where: { productCode: 'PL' } });
    const loanPurpose = await loanPurposeRepo.findOne({ where: { id: 1 } });

    if (!product || !loanPurpose) {
      throw new Error('Product or LoanPurposeMaster not found for seeding LoanApplication');
    }

    let loanApp = await loanAppRepo.findOne({ where: { applicationRefNum: 'PL-IN-2406-00001' } });
    if (!loanApp) {
      loanApp = loanAppRepo.create({
        productId: product.id,
        userId: 1,
        loanPurposeId: loanPurpose.id,
        applicantType: 'individual',
        statusId: 1,
        applicationRefNum: 'PL-IN-2406-00001',
      });
      loanApp = await loanAppRepo.save(loanApp);
      console.log('Inserted LoanApplication with ref:', loanApp.applicationRefNum);
    } else {
      console.log('LoanApplication seed already exists with ref:', loanApp.applicationRefNum);
    }

    return loanApp;
  }
}
