import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { LoanStatusMaster } from 'src/loan-status-master/entity/loan-status-master.entity';

@Injectable()
export class LoanStatusMasterSeeder {
  constructor(private readonly dataSource: DataSource) {}

  async run(): Promise<void> {
    const repo = this.dataSource.getRepository(LoanStatusMaster);
    const statuses = [
      { label: 'New', code: 'NEW', isActive: true, isTerminal: false },
      { label: 'ApprovedL1', code: 'APPROVEDL1', isActive: true, isTerminal: false },
      { label: 'ApprovedL2', code: 'APPROVEDL2', isActive: true, isTerminal: true },
      { label: 'Rejected', code: 'REJECTED', isActive: true, isTerminal: true },
      { label: 'Disbursed', code: 'DISBURSED', isActive: true, isTerminal: true },
    ];

    for (const item of statuses) {
      const exists = await repo.findOneBy({ code: item.code });
      if (!exists) {
        await repo.save(repo.create(item));
      }
    }
  }
}
