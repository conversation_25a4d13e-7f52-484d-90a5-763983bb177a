import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { LookupMaster } from 'src/lookup-master/entity/lookup-master.entity';

@Injectable()
export class LookupMasterSeeder {
  constructor(private dataSource: DataSource) {}

  async run() {
    const repo = this.dataSource.getRepository(LookupMaster);
    const data = [
      { category: 'ACCOMMODATION_TYPE', code: 'Self_owned', value: 'Self-Owned' },
      { category: 'ACCOMMODATION_TYPE', code: 'family_owned', value: 'Family Owned' },
      { category: 'ACCOMMODATION_TYPE', code: 'rental_living_alone', value: 'Rental Living Alone' },
      {
        category: 'ACCOMMODATION_TYPE',
        code: 'rental_living_with_family',
        value: 'Rental Living with Family',
      },
      { category: 'ACCOMMODATION_TYPE', code: 'pg_accommodation', value: 'PG Accommodation' },
      { category: 'ACCOMMODATION_TYPE', code: 'company_provided', value: 'Company-Provided' },
    ];

    for (const item of data) {
      const exists = await repo.findOneBy({ category: item.category, code: item.code });
      if (!exists) {
        await repo.save(repo.create(item));
      }
    }
  }
}
