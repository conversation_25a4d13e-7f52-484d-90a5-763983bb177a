import { DataSource } from 'typeorm';
import {
  ApplicantDetail,
  MaritalStatus,
} from '../applicant-details/entity/applicant-details.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';
import { EducationalQualification } from 'src/common/enums/education.enum';

export class ApplicantDetailsSeeder {
  async run(dataSource: DataSource): Promise<ApplicantDetail> {
    const applicantRepo = dataSource.getRepository(ApplicantDetail);
    const loanAppRepo = dataSource.getRepository(LoanApplication);

    // Get loan application
    const loanApp = await loanAppRepo.findOne({
      where: { applicationRefNum: 'PL-IN-2406-00001' },
    });

    if (!loanApp) {
      throw new Error('Loan application not found for seeding applicant details');
    }

    // Create applicant details
    let applicant = await applicantRepo.findOne({
      where: { applicationId: loanApp.id },
    });
    if (!applicant) {
      applicant = applicantRepo.create({
        applicationId: loanApp.id,
        name: '<PERSON>',
        dob: '1985-05-15',
        gender: 'M',
        pan: '**********',
        employmentType: 'Salaried',
        salaryDisbursementMode: 'Bank Transfer',
        maritalStatus: MaritalStatus.MARRIED,
        dependentsCount: 2,
        education: EducationalQualification.GRADUATE,
        companyName: 'Tech Solutions Ltd',
        currentExp: 5.5,
        totalExp: 10.2,
        workEmail: '<EMAIL>',
        isWorkEmailVerified: true,
        creditAnalysis: {},
      });
      applicant = await applicantRepo.save(applicant);
      console.log('Inserted ApplicantDetails for application:', loanApp.applicationRefNum);
    } else {
      console.log(
        'ApplicantDetails seed already exists for application:',
        loanApp.applicationRefNum
      );
    }

    return applicant;
  }
}
