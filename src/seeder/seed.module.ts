import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LookupMasterSeeder } from './lookup-master.seeder';
import { LookupMaster } from 'src/lookup-master/entity/lookup-master.entity';
import { dataSourceOptions } from 'src/db/data-source';
import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';
import { LoanStatusMaster } from 'src/loan-status-master/entity/loan-status-master.entity';
import { LoanPurposeMasterSeeder } from './loan-purpose-master.seeder';
import { LoanStatusMasterSeeder } from './loan-status-master.seeder';
import { Product } from 'src/product/entity/product.entity';
import { ProductSeeder } from './product.seeder';
import { LoanApplicationSeeder } from './loan-application.seeder';
import { Address } from 'src/addresses/entity/address.entity';
import { ApplicantDetail } from 'src/applicant-details/entity/applicant-details.entity';
import { ApplicantDetailsSeeder } from './applicant-details.seeder';
@Module({
  imports: [
    TypeOrmModule.forRoot(dataSourceOptions),
    TypeOrmModule.forFeature([
      LookupMaster,
      LoanStatusMaster,
      LoanPurposeMaster,
      Product,
      Address,
      ApplicantDetail,
    ]),
  ],
  providers: [
    LookupMasterSeeder,
    LoanStatusMasterSeeder,
    LoanPurposeMasterSeeder,
    ProductSeeder,
    // LoanApplicationSeeder,
    // ApplicantDetailsSeeder,
  ],
})
export class SeederModule {}
