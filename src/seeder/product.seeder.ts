import { DataSource } from 'typeorm';
import { Product } from 'src/product/entity/product.entity';

export class ProductSeeder {
  async run(dataSource: DataSource): Promise<Product> {
    const repo = dataSource.getRepository(Product);
    let product = await repo.findOne({ where: { productCode: 'PL' } });
    if (!product) {
      product = await repo.save({
        productCode: 'PL',
        productName: 'Personal Loan',
        isActive: true,
        metadata: { description: 'Loan for personal use' },
      });
      console.log('Inserted product:', product.productCode);
    } else {
      console.log('Product already exists:', product.productCode);
    }
    return product;
  }
}
