import { NestFactory } from '@nestjs/core';
import { SeederModule } from './seed.module';
import { ProductSeeder } from './product.seeder';
import { LookupMasterSeeder } from './lookup-master.seeder';
import { LoanPurposeMasterSeeder } from './loan-purpose-master.seeder';
import { LoanStatusMasterSeeder } from './loan-status-master.seeder';
import { DataSource } from 'typeorm';
import { LoanApplicationSeeder } from './loan-application.seeder';
import { ApplicantDetailsSeeder } from './applicant-details.seeder';
import { AddressSeeder } from './address.seeder';

type SeederEntry = {
  instance: any;
  params?: () => Promise<any[]> | any[];
};

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(SeederModule);

  try {
    const dataSource = app.get<DataSource>(DataSource);

    const productSeeder = app.get<ProductSeeder>(ProductSeeder);
    const lookupMasterSeeder = app.get<LookupMasterSeeder>(LookupMasterSeeder);
    const loanStatusMasterSeeder = app.get<LoanStatusMasterSeeder>(LoanStatusMasterSeeder);
    const loanPurposeMasterSeeder = app.get<LoanPurposeMasterSeeder>(LoanPurposeMasterSeeder);
    const loanApplicationSeeder = app.get<LoanApplicationSeeder>(LoanApplicationSeeder);
    const applicantDetailsSeeder = app.get<ApplicantDetailsSeeder>(ApplicantDetailsSeeder);
    const addressSeeder = app.get<AddressSeeder>(AddressSeeder);

    let productId: number | null = null;

    const seeders: SeederEntry[] = [
      {
        instance: productSeeder,
        params: async () => [dataSource],
      },
      {
        instance: lookupMasterSeeder,
        params: () => [],
      },
      {
        instance: loanStatusMasterSeeder,
        params: () => [],
      },
      {
        instance: loanPurposeMasterSeeder,
        params: async () => {
          if (!productId) {
            throw new Error('productId is required for LoanPurposeMasterSeeder');
          }
          return [dataSource, productId];
        },
      },
      {
        instance: loanApplicationSeeder,
        params: () => [dataSource],
      },
      {
        instance: applicantDetailsSeeder,
        params: () => [dataSource],
      },
      {
        instance: addressSeeder,
        params: () => [dataSource],
      },
    ];

    for (const seederEntry of seeders) {
      const args = (await seederEntry.params?.()) || [];
      const result = await seederEntry.instance.run(...args);

      if (seederEntry.instance === productSeeder) {
        if (!result?.id) {
          throw new Error('ProductSeeder did not return valid product with id');
        }
        productId = result.id;
        console.log(`[Seeder] Product seeded with ID: ${productId}`);
      } else {
        console.log(`[Seeder] ${seederEntry.instance.constructor.name} completed.`);
      }
    }

    console.log('[Seeder] All seeders executed successfully.');
  } catch (error) {
    console.error('[Seeder] Error during seeding:', error);
  } finally {
    await app.close();
  }
}

bootstrap().catch((err) => {
  console.error('Application failed to seed', err);
  // process.exit(1);
});

