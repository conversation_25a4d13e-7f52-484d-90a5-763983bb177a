import { LoanPurposeMaster } from 'src/loan-purpose-master/entity/loan-purpose-master.entity';
import { DataSource } from 'typeorm';

export class LoanPurposeMasterSeeder {
  async run(dataSource: DataSource, productId: number) {
    if (!productId) throw new Error('Product ID is required');

    const repo = dataSource.getRepository(LoanPurposeMaster);

    const exists = await repo.findOne({ where: { productId, purpose: 'Home Renovation' } });
    if (!exists) {
      await repo.save({
        productId,
        purpose: 'Home Renovation',
        isActive: true,
      });
      console.log('Inserted loan purpose for productId:', productId);
    } else {
      console.log('Loan purpose already exists for productId:', productId);
    }
  }
}
