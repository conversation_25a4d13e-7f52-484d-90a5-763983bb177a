import { DataSource } from 'typeorm';
import { AccommodationType, Address, AddressType } from 'src/addresses/entity/address.entity';

export class AddressSeeder {
  async run(dataSource: DataSource): Promise<Address> {
    const addressRepo = dataSource.getRepository(Address);

    // Create residential address
    let residentialAddress = await addressRepo.findOne({
      where: {
        entityType: 'user',
        entityId: 1,
        type: AddressType.RESIDENTIAL,
      },
    });

    if (!residentialAddress) {
      residentialAddress = addressRepo.create({
        addressLine1: '123 Main Street',
        addressLine2: 'Apartment 4B',
        pincode: '110001',
        city: 'New Delhi',
        state: 'Delhi',
        type: AddressType.RESIDENTIAL,
        entityType: 'user',
        entityId: 1,
        isVerified: true,
        accommodationType: AccommodationType.SELF_OWNED,
      });
      residentialAddress = await addressRepo.save(residentialAddress);
      console.log('Inserted Residential Address for user 1');
    } else {
      console.log('Residential Address seed already exists for user 1');
    }

    // Create office address
    let officeAddress = await addressRepo.findOne({
      where: {
        entityType: 'user',
        entityId: 1,
        type: AddressType.OFFICE,
      },
    });

    if (!officeAddress) {
      officeAddress = addressRepo.create({
        addressLine1: '456 Business Park',
        addressLine2: 'Tech Hub Building',
        pincode: '400001',
        city: 'Mumbai',
        state: 'Maharashtra',
        type: AddressType.OFFICE,
        entityType: 'user',
        entityId: 1,
        isVerified: true,
      });
      officeAddress = await addressRepo.save(officeAddress);
      console.log('Inserted Office Address for user 1');
    } else {
      console.log('Office Address seed already exists for user 1');
    }

    return residentialAddress;
  }
}
