import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LendingPartner } from 'src/lending-partners/entity/lending-partner.entity';
import { Product } from 'src/product/entity/product.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';

@Entity('partner_product_mapping')
export class PartnerProductMapping extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'partner_id' })
  partnerId: number;

  @Column({ name: 'product_id' })
  productId: number;

  @ManyToOne(() => LendingPartner, (partner) => partner.partnerProductMappings)
  @JoinColumn({ name: 'partner_id' })
  lendingPartner: LendingPartner;

  @ManyToOne(() => Product, (product) => product.partnerProductMappings)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ name: 'ruleset_id', nullable: true })
  rulesetId: string;
}
