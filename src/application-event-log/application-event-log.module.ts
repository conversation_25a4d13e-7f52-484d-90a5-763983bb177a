import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationEventLog } from './entity/application-event-log.entity';
import { ApplicationEventLogService } from './application-event-log.service';
import { ApplicationEventLogController } from './application-event-log.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ApplicationEventLog])],
  controllers: [ApplicationEventLogController],
  providers: [ApplicationEventLogService],
  exports: [ApplicationEventLogService],
})
export class ApplicationEventLogModule {}
