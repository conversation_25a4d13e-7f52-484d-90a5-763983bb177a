import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index } from 'typeorm';
import { CustomBaseEntity } from 'src/common/entity/base.entity';
import { LoanApplication } from 'src/loan-application/entity/loan-application.entity';

@Entity({ name: 'application_event_log' })
@Index('IDX_APP_EVENT_APP_ID', ['applicationId'])
export class ApplicationEventLog extends CustomBaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'application_id', type: 'int' })
  applicationId: number;

  @ManyToOne(() => LoanApplication, (app) => app.eventLogs, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'application_id' })
  application: LoanApplication;

  @Column({ name: 'event_type', type: 'varchar', length: 255 })
  eventType: string;
}
