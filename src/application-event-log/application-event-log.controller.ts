import { Controller, Get, Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { Roles } from 'src/common/decorators/role.decorator';
import { DecodeTokenGuard } from 'src/common/guards/decode-token.guard';
import { RoleGuard } from 'src/common/guards/roles.guard';
import { fixed_response } from 'src/utils/response-util';
import { ApplicationEventLogService } from './application-event-log.service';

@UseGuards(DecodeTokenGuard, RoleGuard)
@Roles('admin', 'credit-officer')
@Controller('application-event-log')
export class ApplicationEventLogController {
  constructor(private readonly applicationEventLogService: ApplicationEventLogService) {}

  @Get('application/:id')
  async getApplicationEventLogs(@Param('id', ParseIntPipe) id: number) {
    return fixed_response(await this.applicationEventLogService.getApplicationEventLogs(id));
  }
}
