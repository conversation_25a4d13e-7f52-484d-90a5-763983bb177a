import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ApplicationEventLog } from './entity/application-event-log.entity';
import { Message } from 'src/utils/response-util';
import { CreateApplicationEventLogDto } from './dto/create-application-event-log.dto';

@Injectable()
export class ApplicationEventLogService {
  constructor(
    @InjectRepository(ApplicationEventLog)
    private readonly eventLogRepo: Repository<ApplicationEventLog>
  ) {}

  async createEventLog(dto: CreateApplicationEventLogDto): Promise<Message> {
    try {
      const logEntry = this.eventLogRepo.create(dto);
      const saved = await this.eventLogRepo.save(logEntry);
      return {
        success: true,
        data: saved.id,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create event log',
      };
    }
  }
}
