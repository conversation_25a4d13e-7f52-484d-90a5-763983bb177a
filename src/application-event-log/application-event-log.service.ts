import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ApplicationEventLog } from './entity/application-event-log.entity';
import { Repository } from 'typeorm';
import { Message } from 'src/utils/response-util';

@Injectable()
export class ApplicationEventLogService {
  constructor(
    @InjectRepository(ApplicationEventLog) private readonly logRepo: Repository<ApplicationEventLog>
  ) {}

  async getApplicationEventLogs(id: number): Promise<Message> {
    const logs = await this.logRepo.find({
      where: { applicationId: id },
    });
    return { success: true, message: 'Logs fetched successfully', data: logs };
  }
}
